<?php

namespace App\Http\Controllers;

use App\Helpers\ApiHelper;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use App\Services\PaymentGateway\PaymentGatewayFactory;

class XenditController extends Controller
{
    public function callbackId(Request $request): JsonResponse
    {
        // Headers
        $callbackToken = $request->header('x-callback-token');

        if ($callbackToken !== env('XENDIT_WEBHOOK_TOKEN_ID')) {
            return ApiHelper::errorRes2([
                'message' => 'Unauthorized',
                'rc' => 401,
            ]);
        }

        $event = $request->event;
        $data = $request->data;

        return PaymentGatewayFactory::make('27654')->handleCallback($event, $data);
    }

    public function callbackPh(Request $request): JsonResponse
    {
        // Headers
        $callbackToken = $request->header('x-callback-token');

        if ($callbackToken !== env('XENDIT_WEBHOOK_TOKEN_PH')) {
            return ApiHelper::errorRes2([
                'message' => 'Unauthorized',
                'rc' => 401,
            ]);
        }

        $event = $request->event;
        $data = $request->data;

        return PaymentGatewayFactory::make('27654')->handleCallback($event, $data);
    }

    public function callbackTh(Request $request): JsonResponse
    {
        // Headers
        $callbackToken = $request->header('x-callback-token');

        if ($callbackToken !== env('XENDIT_WEBHOOK_TOKEN_TH')) {
            return ApiHelper::errorRes2([
                'message' => 'Unauthorized',
                'rc' => 401,
            ]);
        }

        $event = $request->event;
        $data = $request->data;

        return PaymentGatewayFactory::make('27654')->handleCallback($event, $data);
    }

    public function callbackMy(Request $request): JsonResponse
    {
        // Headers
        $callbackToken = $request->header('x-callback-token');

        if ($callbackToken !== env('XENDIT_WEBHOOK_TOKEN_MY')) {
            return ApiHelper::errorRes2([
                'message' => 'Unauthorized',
                'rc' => 401,
            ]);
        }

        $event = $request->event;
        $data = $request->data;

        return PaymentGatewayFactory::make('27654')->handleCallback($event, $data);
    }

    public function callbackVn(Request $request): JsonResponse
    {
        // Headers
        $callbackToken = $request->header('x-callback-token');

        if ($callbackToken !== env('XENDIT_WEBHOOK_TOKEN_VN')) {
            return ApiHelper::errorRes2([
                'message' => 'Unauthorized',
                'rc' => 401,
            ]);
        }

        $event = $request->event;
        $data = $request->data;

        return PaymentGatewayFactory::make('27654')->handleCallback($event, $data);
    }

    public function simulatePayment(Request $request): JsonResponse
    {
        return PaymentGatewayFactory::make('27654')->simulatePayment($request);
    }
}
