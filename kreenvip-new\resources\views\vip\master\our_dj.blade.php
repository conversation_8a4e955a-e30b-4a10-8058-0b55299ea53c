@extends('vip.app')

@section('title', 'Our DJ - Princess <PERSON><PERSON> & DJ <PERSON><PERSON><PERSON> - KREEN VIP')

@push('styles')
<style>
/* Full width section */
.full-width-section {
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    padding-left: calc(50vw - 50%);
    padding-right: calc(50vw - 50%);
}

/* Event poster styling */
.event-poster {
    background: linear-gradient(135deg, rgba(0,0,0,0.3), rgba(0,0,0,0.7));
    border: 2px solid #D4AF37;
    border-radius: 1rem;
    overflow: hidden;
    position: relative;
    height: 400px;
}

.event-poster::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(0,0,0,0.4), rgba(0,0,0,0.6));
    z-index: 1;
}

.event-content {
    position: relative;
    z-index: 2;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 2rem;
}

/* Performer images */
.performer-image {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #D4AF37;
}

/* Two column layout */
.two-column-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: start;
}

@media (max-width: 1024px) {
    .two-column-layout {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
}

/* Right side content styling */
.event-details {
    padding-top: 2rem;
}

/* Dots indicator */
.dots-indicator {
    display: flex;
    justify-content: center;
    space-x: 0.5rem;
    margin-top: 1rem;
}

.dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.3);
}

.dot.active {
    background-color: #D4AF37;
}
</style>
@endpush

@section('content')
<!-- Container dengan Background Image untuk DJ Page -->
<div class="explore-nights-bg-container">
    <!-- Main Content Section -->
    <section class="explore-nights-content py-12">
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
            
            <!-- Two Column Layout -->
            <div class="two-column-layout mb-12">
                <!-- Left Side - Event Poster -->
                <div class="event-poster" style="background-image: url('https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'); background-size: cover; background-position: center;">
                    <div class="event-content">
                        <!-- Top Section with Date -->
                        <div class="flex justify-end items-start">
                            <div class="text-center bg-white bg-opacity-20 rounded-lg px-3 py-2">
                                <div class="text-white text-sm font-medium">THU</div>
                                <div class="text-white text-xl font-bold">JUN</div>
                                <div class="text-white text-2xl font-bold">19<sup class="text-sm">TH</sup></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Side - Event Details -->
                <div class="event-details">
                    <p class="text-gray-400 text-sm mb-2">UPCOMING EVENTS</p>
                    <h1 class="text-white text-3xl font-bold mb-6">Princess Joana & DJ Yasmin</h1>
                    
                    <div class="space-y-4 mb-8">
                        <div class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                            </svg>
                            <span class="text-white">The H Club SCBD</span>
                        </div>
                        
                        <div class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                            </svg>
                            <span class="text-white">Thursday, 19 June 2025</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- About Event Section -->
            <div class="mb-12">
                <h3 class="text-white text-xl font-bold mb-6">About Event</h3>
                <div class="text-gray-300 text-sm space-y-4 leading-relaxed max-w-4xl">
                    <p>We heard you loud. So guess what? FRONT TO FRONT is the floor again. Thursday for house music 🔥</p>
                    
                    <p>@dj_yasmin & @dj_joana in one stage. Two styles, one rhythm.</p>
                    
                    <div class="space-y-1">
                        <p>THURSDAY - JUNE 19th</p>
                        <p>THE H CLUB</p>
                        <p>0811 1024 5996 (WA Only)</p>
                    </div>
                    
                    <p>Or reserve your spot now through HOLYWINGS App so you don't miss out!</p>
                    
                    <p class="text-kreen-gold">#TheHClub #NeverStopFlying #BestNightClubAsia</p>
                </div>
            </div>

            <!-- Performing Section -->
            <div class="mb-12">
                <h3 class="text-white text-xl font-bold mb-6">Performing</h3>
                <div class="flex space-x-8">
                    <div class="text-center">
                        <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80" 
                             alt="Princess Joana" 
                             class="performer-image mx-auto mb-3">
                        <p class="text-white text-sm font-medium">Princess Joana</p>
                    </div>
                    <div class="text-center">
                        <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80" 
                             alt="DJ Yasmin" 
                             class="performer-image mx-auto mb-3">
                        <p class="text-white text-sm font-medium">DJ Yasmin</p>
                    </div>
                </div>
            </div>

            <!-- Location Section -->
            <div class="mb-12">
                <h3 class="text-white text-xl font-bold mb-6">Location</h3>
                
                <!-- Map Container -->
                <div class="bg-gray-800 rounded-lg h-48 w-full max-w-2xl mb-4 flex items-center justify-center relative overflow-hidden">

                                <div class="absolute inset-0 bg-gradient-to-br from-gray-700 to-gray-900"></div>
                                <div class="relative z-10 text-center">
                                    <svg class="w-12 h-12 text-kreen-gold mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                                    </svg>
                                    <p class="text-gray-400 text-sm">Interactive Map</p>
                                </div>
                                <!-- Map controls -->
                                <div class="absolute top-2 right-2 flex flex-col space-y-1">
                                    <button class="w-8 h-8 bg-white bg-opacity-90 rounded flex items-center justify-center text-gray-800 hover:bg-opacity-100">
                                        <span class="text-lg font-bold">+</span>
                                    </button>
                                    <button class="w-8 h-8 bg-white bg-opacity-90 rounded flex items-center justify-center text-gray-800 hover:bg-opacity-100">
                                        <span class="text-lg font-bold">−</span>
                                    </button>
                                </div>
                </div>
                
                <!-- Location Details -->
                <div>
                    <h4 class="text-white font-medium mb-2">The H Club SCBD</h4>
                    <div class="flex items-start space-x-2">
                        <svg class="w-4 h-4 text-kreen-gold mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                        </svg>
                        <p class="text-gray-300 text-sm">Jl. Jend. Sudirman Kav 52-53 Jl. Scbd No. Lot 19, Senayan, Kec. Kby. Baru, Kota Jakarta Selatan, Daerah Khusus Ibukota Jakarta 12190</p>
                    </div>
                </div>
            </div>

            <!-- Social Media Section -->
            <div class="mb-12">
                <h3 class="text-white text-xl font-bold mb-6">Social Media</h3>
                <div class="flex space-x-6">
                    <a href="#" class="flex items-center space-x-2 text-gray-400 hover:text-kreen-gold transition-colors">
                        
                        <span class="text-sm">@dj_joana</span>
                    </a>
                    <a href="#" class="flex items-center space-x-2 text-gray-400 hover:text-kreen-gold transition-colors">
                      
                        <span class="text-sm">@dj_yasmin</span>
                    </a>
                </div>
            </div>

        </div>
    </section>
</div>
@endsection
