<?php

namespace App\Services\PaymentGateway;

use InvalidArgumentException;
use App\Services\PaymentGateway\XenditService;
use App\Services\PaymentGateway\WebhookHandler;

class PaymentGatewayFactory
{
    public static function make(string $gateway): PaymentGatewayInterface
    {
        switch ($gateway) {
            case '27654':
                return new XenditService(new WebhookHandler());
            default:
                throw new InvalidArgumentException("Unsupported payment gateway: {$gateway}");
        }
    }
}
