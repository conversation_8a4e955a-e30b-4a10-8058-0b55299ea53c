<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class BarVenue extends Model
{
    protected $table = 'tbl_bar_venue';
    
    protected $fillable = [
        'id',
        'id_merchant',
        'nama',
        'informasi',
        'banner',
        'currency',
        'lat',
        'lng',
        'id_negara',
        'id_propinsi',
        'id_kota',
        'id_kelurahan',
        'alamat',
        'slug',
        'flag_aktif',
        'username_ig',
        'no_wa'
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // Scope untuk venue yang aktif
    public function scopeActive($query)
    {
        return $query->where('flag_aktif', '1');
    }

    // Relasi dengan merchant
    public function merchant()
    {
        return $this->belongsTo(User::class, 'id_merchant', 'id');
    }

    // Relasi dengan events
    public function events()
    {
        return $this->hasMany(BarEvent::class, 'id_venue', 'id');
    }
}
