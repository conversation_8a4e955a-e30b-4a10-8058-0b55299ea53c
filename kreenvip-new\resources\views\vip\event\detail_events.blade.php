@extends('vip.app')

@section('title', $event->nama)

@push('styles')
    <style>
        #map {
            height: 326px;
            border-radius: 12px;
        }
        /* Full width section */
        .full-width-section {
            width: 100vw;
            margin-left: calc(-50vw + 50%);
            padding-left: calc(50vw - 50%);
            padding-right: calc(50vw - 50%);
        }

        /* Event poster styling */
        .event-poster {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.7));
            border: 2px solid #D4AF37;
            border-radius: 1rem;
            overflow: hidden;
            position: relative;
            height: 400px;
        }

        .event-poster::before {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.6));
            z-index: 1;
        }

        .event-content {
            position: relative;
            z-index: 2;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding: 2rem;
        }

        /* Performer images */
        .performer-image {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #D4AF37;
        }

        /* Two column layout */
        .two-column-layout {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
            align-items: start;
        }

        @media (max-width: 1024px) {
            .two-column-layout {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
        }

        /* Right side content styling */
        .event-details {
            padding-top: 2rem;
        }

        /* Dots indicator */
        .dots-indicator {
            display: flex;
            justify-content: center;
            space-x: 0.5rem;
            margin-top: 1rem;
        }

        .dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.3);
        }

        .dot.active {
            background-color: #D4AF37;
        }
    </style>
@endpush

@section('content')
    <!-- Container dengan Background Image untuk DJ Page -->
    <div class="explore-nights-bg-container">
        <!-- Main Content Section -->
        <section class="explore-nights-content py-12">
            <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">

                <!-- Two Column Layout -->
                <div class="two-column-layout mb-12">
                    <!-- Left Side - Event Poster -->
                    <div class="event-poster"
                        style="background-image: url('{{ $event->gambar }}'); background-size: cover; background-position: center;">
                        <div class="event-content">
                            <!-- Top Section with Date -->
                            <div class="flex justify-end items-start">
                                <div class="text-center bg-kreen-gold bg-opacity-20 rounded-lg px-3 py-2 uppercase">
                                    <div class="text-white text-xl font-bold">
                                        {{ \Carbon\Carbon::parse($event->tanggal)->format('M') }}</div>
                                    <div class="text-white text-2xl font-bold">
                                        {{ \Carbon\Carbon::parse($event->tanggal)->format('jS') }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right Side - Event Details -->
                    <div class="event-details">
                        <p class="text-gray-400 text-sm mb-2">UPCOMING EVENTS</p>
                        <h1 class="text-white text-3xl font-bold mb-6">{{ $event->nama }}</h1>

                        <div class="space-y-4 mb-8">
                            <div class="flex items-center space-x-3">
                                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                                        clip-rule="evenodd" />
                                </svg>
                                <span class="text-white">{{ $event->venue_name }}</span>
                            </div>

                            <div class="flex items-center space-x-3">
                                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                                        clip-rule="evenodd" />
                                </svg>
                                <span
                                    class="text-white">{{ \Carbon\Carbon::parse($event->tanggal)->format('l, d F Y') }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- About Event Section -->
                @if (!empty($event->deskripsi))
                    <div class="mb-12">
                        <h3 class="text-white text-xl font-bold mb-6">About Event</h3>
                        <div class="text-gray-300 text-sm space-y-4 leading-relaxed max-w-4xl">
                            {!! $event->deskripsi !!}
                        </div>
                    </div>
                @endif

                <!-- Performing Section -->
                @if (!empty($djs) && count($djs) > 0)
                    <div class="mb-12">
                        <h3 class="text-white text-xl font-bold mb-6">Performing</h3>
                        <div class="flex space-x-8">
                            @foreach ($djs as $dj)
                                <a href="{{ route('dj.profileDj', $dj->slug) }}" class="text-center">
                                    <img src="{{ $dj->gambar }}" alt="{{ $dj->nama }}"
                                        class="performer-image mx-auto mb-3">
                                    <p class="text-white text-sm font-medium">{{ $dj->nama }}</p>
                                </a>
                            @endforeach
                        </div>
                    </div>
                @endif

                @if (!empty($event->venue_lat) || !empty($event->venue_lng))
                <!-- Location Section -->
                <div class="mb-12">
                    <h3 class="text-white text-xl font-bold mb-6">Location</h3>

                    <!-- Map Container -->
                    <div id="map" class="mb-3"></div>

                    <!-- Location Details -->
                    <div>
                        <h4 class="text-white font-medium mb-2">{{ $event->venue_name }}</h4>
                        <div class="flex items-start space-x-2">
                            <svg class="w-4 h-4 text-kreen-gold mt-0.5 flex-shrink-0" fill="currentColor"
                                viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                                    clip-rule="evenodd" />
                            </svg>
                            <p class="text-gray-300 text-sm">{{ $event->venue_address }}</p>
                        </div>
                    </div>
                </div>
                @endif

                <!-- Social Media Section -->
                <div class="mb-12">
                    <h3 class="text-white text-xl font-bold mb-6">Social Media</h3>
                    <div class="flex space-x-6">
                        <a href="#"
                            class="flex items-center space-x-2 text-gray-400 hover:text-kreen-gold transition-colors">

                            <span class="text-sm">@dj_joana</span>
                        </a>
                        <a href="#"
                            class="flex items-center space-x-2 text-gray-400 hover:text-kreen-gold transition-colors">

                            <span class="text-sm">@dj_yasmin</span>
                        </a>
                    </div>
                </div>

            </div>
        </section>
    </div>
@endsection

@push('scripts')

    <script>
        function initMap() {
            const position = {
                lat: {{ $event->venue_lat }},
                lng: {{ $event->venue_lng }}
            };
            const map = new google.maps.Map(document.getElementById("map"), {
                center: position,
                zoom: 16,
                mapId: "DEMO_MAP_ID",
                disableDefaultUI: true,
                zoomControl: true,
                fullscreenControl: true,
            });
            const marker = new google.maps.marker.AdvancedMarkerElement({
                position: position,
                title: 'Lokasi',
                map: map,
            });
        }
    </script>
     <script
     src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDsALC8ufBkAAp_aH6fvCFKkK-Mi0oneaA&callback=initMap&loading=async&region=ID&language={{ app()->getLocale() }}&libraries=marker"
     defer></script>
@endpush
