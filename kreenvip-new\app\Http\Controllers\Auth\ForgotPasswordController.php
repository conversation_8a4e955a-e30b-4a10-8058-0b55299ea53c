<?php

namespace App\Http\Controllers\Auth;

use DB;
use App\Models\User;
use Illuminate\Http\Request;
use App\Mail\SendForgotPassword;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Mail;

class ForgotPasswordController extends Controller
{
    public function index() {
        return view('auth.forgot-password.index');
    }

    public function process(Request $request) {
        $request->validate([
            'email' => 'required|email'
        ]);
        $email = $request->email;
        $user = User::where('email', $email)->first();
        if(!$user) {
            abort(404);
        }
        $name = $user->name;
        $enc_email = encrypt($email);
        $timestamp = time();
        $queryData = [
            'd' => $enc_email,
            'ts' => $timestamp
        ];
        $url = route('auth.forgotPassword.reset', $queryData);
        Mail::to($email)->send(new SendForgotPassword(['url' => $url, 'name' => $name, 'email' => $email]));


        return redirect()->route('auth.forgotPassword.successSend');
    }

    public function successSend() {
        return view('auth.forgot-password.success-send');
    }

    private function expiredLink() {
        return view('errors.410');
    }

    public function reset(Request $request) {
        $d = $request->d;
        $timestamp = $request->ts;
        if(time() - $timestamp > 1800) {
            abort(404);
            // return view('auth.forgot-password.expired');
            // return $this->expiredLink();
            // return view('errors.410');
        }

        return view('auth.forgot-password.reset', [
            'd' => $d,
            'ts' => $timestamp
        ]);
    }

    public function resetProcess(Request $request) {
        $request->validate([
            'd' => 'required',
            'ts' => 'required',
            'new_password' => 'required|min:6|confirmed',
        ]);
        $d = $request->d;
        $timestamp = $request->ts;
        if(time() - $timestamp > 1800) {
            abort(404);
        }
        $email = decrypt($d);
        $new_password = bcrypt($request->new_password);
        User::where('email', $email)->update([
            'password' => $new_password
        ]);
        return redirect()->route('auth.forgotPassword.successReset');
    }

    public function successReset() {
        return view('auth.forgot-password.success-reset');
    }
}
