<?php

namespace App\Http\Controllers;

use Exception;
use Carbon\Carbon;
use App\Helpers\PG;
use App\Helpers\Ticket;
use App\Helpers\Invoice;
use App\Helpers\ApiHelper;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\Http\Requests\GuestInfoRequest;
use Illuminate\Validation\ValidationException;
use App\Services\PaymentGateway\PaymentGatewayFactory;

class OrderController extends Controller
{

    public function guestInfo(GuestInfoRequest $request)
    {
        // Get Input

        $id_venue = $request->input('id_venue');
        $date = $request->input('date');
        $date = date('l, d F Y', strtotime($date));
        $tickets = collect(json_decode($request->input('tickets')));
        if ($tickets->isEmpty()) {
            throw ValidationException::withMessages([
                'tickets' => 'Tiket tidak boleh kosong',
            ]);
        }
        // Get Venue
        $venue = DB::table('tbl_bar_venue')->select('id', 'nama', 'alamat', 'currency')->where('id', $id_venue)->where('flag_aktif', '1')->where('flag_sale', '1')->first();

        // Get Ticket
        $ticket_ids = $tickets->pluck('id')->toArray();
        $date_index = (int) date('N', strtotime($date));
        $tickets_db = DB::select("SELECT t.id, t.id_venue, t.nama_tiket, t.stok_tersedia, th.id as id_jenis_tiket_harga, th2.id as id_jenis_tiket_harga2,
                                    COALESCE(th.harga, th2.harga, t.default_harga) as harga,
                                    COALESCE(th.stok, th2.stok, t.default_stok) as stok
                                    FROM tbl_bar_jenis_tiket t
                                    LEFT JOIN tbl_bar_jenis_tiket_harga th ON t.id = th.id_jenis_tiket AND th.tanggal = ? AND th.hari IS NULL
                                    LEFT JOIN tbl_bar_jenis_tiket_harga th2 ON t.id = th2.id_jenis_tiket AND th2.tanggal IS NULL AND th2.hari = ?
                                    WHERE t.id IN (" . implode(',', $ticket_ids) . ")
                                    ORDER BY harga ASC, stok ASC", [$date, $date_index]);
        $tickets_data = $tickets->map(function ($ticket) use ($tickets_db) {
            $ticket_db = collect($tickets_db)->firstWhere('id', $ticket->id);
            if ($ticket->qty > $ticket_db?->stok_tersedia) {
                throw ValidationException::withMessages([
                    'tickets' => 'Stok tiket tidak mencukupi',
                ]);
            }
            return [
                'id' => $ticket_db->id,
                'nama_tiket' => $ticket_db->nama_tiket,
                'qty' => $ticket->qty,
                'stok_tersedia' => $ticket_db->stok_tersedia,
                'harga_satuan' => $ticket_db->harga,
                'total_harga' => $ticket_db->harga * $ticket->qty,
                'id_jenis_tiket_harga' => $ticket_db->id_jenis_tiket_harga,
                'id_jenis_tiket_harga2' => $ticket_db->id_jenis_tiket_harga2,
            ];
        });

        // Get Payment Method
        $payment_methods = PG::getPaymentMethods();


        $payment_methods = $payment_methods->groupBy('category_name');
        return view('vip.order.guest_info', compact('venue', 'tickets_data', 'date', 'payment_methods'));

    }

    public function submitGuestInfo(GuestInfoRequest $request)
    {
        $id_venue = $request->input('id_venue');
        $date = $request->input('date');
        $tickets = $request->input('tickets');
        $payment_method_inp = $request->input('payment_method');
        $payment_method_inp = collect(json_decode($payment_method_inp));
        $tickets = collect(json_decode($tickets));

        $venue = DB::table('tbl_bar_venue')->select('id', 'nama', 'alamat', 'currency', 'slug')->where('id', $id_venue)->where('flag_aktif', '1')->where('flag_sale', '1')->first();

        // Get Ticket
        $ticket_ids = $tickets->pluck('id')->toArray();
        $date_index = (int) date('N', strtotime($date));
        $tickets_db = DB::select("SELECT t.id, t.id_venue, t.nama_tiket, t.stok_tersedia,
                                    COALESCE(th.harga, th2.harga, t.default_harga) as harga,
                                    COALESCE(th.stok, th2.stok, t.default_stok) as stok
                                    FROM tbl_bar_jenis_tiket t
                                    LEFT JOIN tbl_bar_jenis_tiket_harga th ON t.id = th.id_jenis_tiket AND th.tanggal = ? AND th.hari IS NULL
                                    LEFT JOIN tbl_bar_jenis_tiket_harga th2 ON t.id = th2.id_jenis_tiket AND th2.tanggal IS NULL AND th2.hari = ?
                                    WHERE t.id IN (" . implode(',', $ticket_ids) . ")
                                    ORDER BY harga ASC, stok ASC", [$date, $date_index]);
        $tickets_data = $tickets->map(function ($ticket) use ($tickets_db) {
            $ticket_db = collect($tickets_db)->firstWhere('id', $ticket->id);
            if ($ticket->qty > $ticket_db?->stok_tersedia) {
                throw ValidationException::withMessages([
                    'tickets' => 'Stok tiket tidak mencukupi',
                ]);
            }
            return [
                'id' => $ticket_db->id,
                'nama_tiket' => $ticket_db->nama_tiket,
                'qty' => $ticket->qty,
                'stok_tersedia' => $ticket_db->stok_tersedia,
                'harga_satuan' => $ticket_db->harga,
                'total_harga' => $ticket_db->harga * $ticket->qty,
                'guest_infos' => $ticket->guest_infos,
            ];
        });

        $payment_method = DB::table('tbl_pg_metod_payget')->where('id', $payment_method_inp['id'])->first();
        $payment_method_category = DB::table('tbl_pg_category_payget')->where('id', $payment_method->id_category_payget)->first();
        $total_payment = $tickets_data->sum('total_harga');
        $fee_flat_pg = $payment_method->fee;
        $fee_final =
            ($total_payment /
                (1 - ($payment_method->fee_percent / 100) * (1 + $payment_method->ppn / 100)) +
                $fee_flat_pg * (1 + $payment_method->ppn / 100)) - $total_payment;
        if ($venue->currency !== 'IDR') {
            $fee_final += $total_payment * 1 / 100;
            $fee_final = ceil($fee_final * 100) / 100;
        } else {
            $fee_final = ceil($fee_final);
        }

        $id_order = Str::random(16);
        $code_invoice = Invoice::generateCodeInvoice();
        $invoice_number = Invoice::generateInvoiceNumber($id_order, $code_invoice);
        $dataOrder = [
            'id' => $id_order,
            'id_venue' => $id_venue,
            'id_user' => Auth::user()->id ?? null,
            'id_pg_metod_payget' => $payment_method_inp['id'],
            'date' => $date,
            'code_invoice' => $code_invoice,
            'invoice_number' => $invoice_number,
            'full_name' => $tickets_data[0]['guest_infos'][0]->full_name,
            'email' => $tickets_data[0]['guest_infos'][0]->email,
            'phone' => $tickets_data[0]['guest_infos'][0]->phone_number,
            'expiry_period' => $payment_method->expired_duration,
            'status' => '3',
            'currency' => $venue->currency,
            'payment_method_category' => $payment_method_category->category_name,
            'payment_method_name' => $payment_method->payment_name,
            'type_pg' => $payment_method_category->code_category_payget,
            'total_qty' => $tickets_data->sum('qty'),
            'disc' => 0,
            'total_price' => $total_payment,
            'fees' => $fee_final,
            'total_amount' => $total_payment + $fee_final,
            'region' => 'ID',
            'version' => 'v1',
            'ip_address' => $request->ip(),
        ];

        $dataTransaction = [
            'payment_method' => $payment_method,
            'payment_method_code_category' => $payment_method_category->code_category_payget,
            'slug' => $venue->slug,
            'bank_code' => $payment_method->bank_code,
            'mobile_number' => $payment_method_inp['mobile_number'] ?? null,
            'id_card' => $payment_method_inp['id_card'] ?? null,
            'card_number' => $payment_method_inp['card_number'] ?? null,
            'expiry_month' => $payment_method_inp['expiry_month'] ?? null,
            'expiry_year' => $payment_method_inp['expiry_year'] ?? null,
            'cvv' => $payment_method_inp['cvv'] ?? null,
            'country' => 'ID',
        ];

        $dataTransaction = array_merge($dataOrder, $dataTransaction);

        try {
            $dataPG = PaymentGatewayFactory::make($payment_method->id_pg_type)->createTransaction($dataTransaction);
            if ($dataPG instanceof JsonResponse) {
                return $dataPG;
            }
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }

        if ($dataPG == null) {
            return ApiHelper::errorRes([
                'message' => 'Gagal membuat transaksi',
            ]);
        }

        $attempt = 1;
        $max_attempt = 3;
        $transaction_delay = 1000;

        while ($attempt <= $max_attempt) {
            try {
                DB::beginTransaction();

                DB::table('tbl_bar_order')->insert($dataOrder);

                $orderDetailData = [];

                foreach ($tickets_data as $ticket) {
                    foreach ($ticket['guest_infos'] as $guest_info) {
                        $orderDetailData[] = [
                            'id' => Str::random(16),
                            'id_order' => $id_order,
                            'id_jenis_tiket' => $ticket['id'],
                            'id_jenis_tiket_harga' => $ticket['id_jenis_tiket_harga'] ?? $ticket['id_jenis_tiket_harga2'] ?? null,
                            'full_name' => $guest_info->full_name,
                            'email' => $guest_info->email,
                            'phone' => $guest_info->phone_number,
                            'qty' => 1,
                            'price' => $ticket['harga_satuan'],
                        ];
                        DB::table('tbl_bar_jenis_tiket')->where('id', $ticket['id'])->update([
                            'stok_tersedia' => DB::raw('stok_tersedia - 1'),
                        ]);
                    }
                }

                if (!empty($orderDetailData)) {
                    DB::table('tbl_bar_order_detail')->insert($orderDetailData);
                }

                $createdRaw = $dataPG['created'];
                $createdClean = substr($createdRaw, 0, 19) . 'Z'; // hanya sampai detik
                $createdAt = Carbon::parse($createdClean)->setTimezone('Asia/Jakarta');
                $expiresAt = $createdAt->copy()->addSeconds($payment_method->expired_duration);
                $pm = $dataPG['payment_method'];
                $pm_cp = $pm['ewallet']['channel_properties']
                    ?? $pm['virtual_account']['channel_properties']
                    ?? $pm['over_the_counter']['channel_properties']
                    ?? $pm['direct_debit']['channel_properties']
                    ?? $pm['card']['channel_properties']
                    ?? $pm['qr_code']['channel_properties']
                    ?? [];

                $card_info = $pm['card']['card_information'] ?? [];
                $customer = $dataPG['customer'] ?? null;
                $actions = collect($dataPG['actions'] ?? []);

                DB::table('tbl_pgxd_payment_method_request')->insert([
                    // Basic payment data
                    'id' => $dataPG['id'],
                    'order_id' => $id_order,
                    'reference_id' => $pm['reference_id'],
                    'business_id' => $dataPG['business_id'],
                    's_status' => $dataPG['status'],
                    'currency' => $dataPG['currency'],
                    'amount' => $dataPG['amount'],
                    'id_pg_metod_payget' => $payment_method->id,
                    'bank_code' => $payment_method->bank_code,
                    'channel_code' => $pm['ewallet']['channel_code']
                        ?? $pm['virtual_account']['channel_code']
                        ?? $pm['over_the_counter']['channel_code']
                        ?? $pm['direct_debit']['channel_code']
                        ?? $pm['card']['channel_code']
                        ?? $pm['qr_code']['channel_code']
                        ?? null,
                    'expiry_period' => (int) ($payment_method->expired_duration / 60),
                    'expires_at' => $pm_cp['expires_at'] ?? $expiresAt->toDateTimeString() ?? null,

                    // Customer
                    'customer_id' => $dataPG['customer_id'] ?? $customer['id'] ?? null,
                    'customer' => $customer ? json_encode($customer) : null,
                    'country' => $dataPG['country'] ?? null,
                    'description' => $dataPG['description'] ?? $pm['description'] ?? null,

                    // Optional metadata
                    'failure_code' => $dataPG['failure_code'] ?? null,
                    'capture_method' => $dataPG['capture_method'] ?? null,
                    'initiator' => $dataPG['initiator'] ?? null,
                    'card_verification_results' => isset($dataPG['card_verification_results']) ? json_encode($dataPG['card_verification_results']) : null,
                    'metadata' => isset($dataPG['metadata']) ? json_encode($dataPG['metadata']) : null,
                    'actions' => isset($dataPG['actions']) ? json_encode($dataPG['actions']) : null,
                    'shipping_information' => isset($dataPG['shipping_information']) ? json_encode($dataPG['shipping_information']) : null,
                    'items' => isset($dataPG['items']) ? json_encode($dataPG['items']) : null,

                    // Payment method & related
                    'payment_method' => isset($pm) ? json_encode($pm) : null,
                    'payment_method_id' => $pm['id'],
                    'type' => $pm['type'],
                    'qr_code' => isset($pm['qr_code']) ? json_encode($pm['qr_code']) : null,
                    'reusability' => $pm['reusability'] ?? null,
                    'card' => isset($pm['card']) ? json_encode($pm['card']) : null,
                    'direct_debit' => isset($pm['direct_debit']) ? json_encode($pm['direct_debit']) : null,
                    'ewallet' => isset($pm['ewallet']) ? json_encode($pm['ewallet']) : null,
                    'over_the_counter' => isset($pm['over_the_counter']) ? json_encode($pm['over_the_counter']) : null,
                    'virtual_account' => isset($pm['virtual_account']) ? json_encode($pm['virtual_account']) : null,

                    // Channel properties & checkout
                    'va_number' => $pm_cp['virtual_account_number'] ?? null,
                    'qr_string' => $actions->where('url_type', 'PRESENT_TO_CUSTOMER')->first()['qr_code'] ?? $pm_cp['qr_string'] ?? null,
                    'success_return_url' => $pm_cp['success_return_url'] ?? null,
                    'failure_return_url' => $pm_cp['failure_return_url'] ?? null,
                    'mobile_number' => $pm_cp['mobile_number'] ?? null,
                    'qr_checkout_string' => $pm_cp['qr_checkout_string'] ?? $actions->where('url_type', 'PRESENT_TO_CUSTOMER')->first()['qr_code'] ?? null,

                    // Checkout URLs
                    'desktop_web_checkout_url' => $actions->where('url_type', 'WEB')->first()['url'] ?? null,
                    'mobile_web_checkout_url' => $actions->where('url_type', 'MOBILE')->first()['url'] ?? null,
                    'mobile_deeplink_checkout_url' => $actions->where('url_type', 'DEEPLINK')->first()['url'] ?? null,

                    // Card specific
                    'skip_three_d_secure' => $pm_cp['skip_three_d_secure'] ?? null,
                    'cardonfile_type' => $pm_cp['cardonfile_type'] ?? null,
                    'masked_card_number' => $card_info['masked_card_number'] ?? null,
                    'expiry_month' => $card_info['expiry_month'] ?? null,
                    'expiry_year' => $card_info['expiry_year'] ?? null,
                    'cardholder_name' => $card_info['cardholder_name'] ?? null,
                    'fingerprint' => $card_info['fingerprint'] ?? null,
                    'type_cc' => $card_info['type'] ?? null,
                    'network_cc' => $card_info['network'] ?? null,
                    'country_cc' => $card_info['country'] ?? null,
                    'issuer_cc' => $card_info['issuer'] ?? null,

                    // Metadata
                    's_created' => $dataPG['created'] ?? null,
                    's_updated' => $dataPG['updated'] ?? null,
                    'type_pay' => $payment_method_category->code_category_payget,
                    'type_account' => env('APP_ENV') === 'production' ? 'production' : 'sandbox',
                    'type_order' => 'bar',
                    'status' => '2',
                    'created_at' => now(),
                ]);

                DB::commit();

                return ApiHelper::successRes2([
                    "message" => 'Sukses membuat order',
                    "data" => [
                        'id_order' => $id_order,
                    ],
                ]);
            } catch (Exception $e) {
                DB::rollBack();
                $attempt++;
                $message = $e->getMessage();
                $error = [
                    'message' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'rc' => 500,
                ];
                if (
                    strpos($message, 'Deadlock found') !== false ||
                    strpos($message, 'Lock wait timeout') !== false ||
                    strpos($message, 'SQLSTATE[40001]') !== false ||
                    strpos($message, 'SQLSTATE[HY000]: General error: 1205') !== false
                ) {
                    Log::warning("Retrying transaction attempt $attempt: " . $message);
                    if ($attempt >= $max_attempt) {
                        return ApiHelper::errorRes2([
                            'message' => 'Internal Server Error',
                            'data' => $error,
                            'rc' => 500,
                        ]);
                    }
                    usleep($transaction_delay * 1000); // Delay retry
                } else {
                    Log::info('Error Xendit Create Transaction: ', ['response' => $error]);
                    return ApiHelper::errorRes2([
                        'message' => 'Internal Server Error',
                        'data' => $error,
                        'rc' => 500,
                    ]);
                }
            }
        }
    }

    public function waitingPayment($id_order)
    {
        $order = DB::table('tbl_bar_order as o')
            ->select(['v.nama as venue_name', 'v.banner as venue_banner', 'v.alamat as venue_address', 'o.id as id_order', 'o.invoice_number', 'o.date', 'xd.expires_at', 'pm.payment_name', 'pm.img', 'xd.va_number', 'o.total_amount', 'o.currency', 'o.total_price', 'o.fees', 'o.status', 'xd.qr_string', 'xd.desktop_web_checkout_url', 'xd.id_pg_metod_payget', 'xd.mobile_web_checkout_url', 'xd.mobile_deeplink_checkout_url', 'xd.mobile_deeplink_checkout_url', 'xd.id as payment_request_id'])
            ->join('tbl_pgxd_payment_method_request as xd', 'o.id', 'xd.order_id')
            ->join('tbl_pg_metod_payget as pm', 'xd.id_pg_metod_payget', 'pm.id')
            ->join('tbl_bar_venue as v', DB::raw("o.id_venue "), 'v.id')
            ->where('o.id', $id_order)->first();
        if ($order->status === '1') {
            return redirect()->route('order.successPayment', ['id_order' => $id_order]);
        }

        // dd($order);

        $expired_at = Carbon::parse($order->expires_at)->setTimezone('Asia/Jakarta');

        if ($expired_at < now()) {
            return redirect()->route('order.expiredPayment', ['id_order' => $id_order]);
        }

        $instructions = PG::getInstructionsByPaymentMethodId($order->id_pg_metod_payget);

        $tickets = Ticket::getOrderedTicketsByOrderId($id_order);

        if (!$order) {
            abort(404);
        }
        return view('vip.order.waiting-payment', [
            'order' => $order,
            'tickets' => $tickets,
            'instructions' => $instructions,
            'expired_at' => $expired_at,
        ]);
    }

    public function successPayment($id_order)
    {
        $order = DB::table('tbl_bar_order as o')
            ->select(['v.nama as venue_name', 'v.banner as venue_banner', 'v.alamat as venue_address', 'o.id as id_order', 'o.invoice_number', 'o.date', 'xd.expires_at', 'pm.payment_name', 'pm.img', 'xd.va_number', 'o.total_amount', 'o.currency', 'o.total_price', 'o.fees', 'o.status', 'xd.qr_string', 'xd.desktop_web_checkout_url', 'xd.id_pg_metod_payget', 'xd.mobile_web_checkout_url', 'xd.mobile_deeplink_checkout_url', 'xd.mobile_deeplink_checkout_url'])
            ->join('tbl_pgxd_payment_method_request as xd', 'o.id', 'xd.order_id')
            ->join('tbl_pg_metod_payget as pm', 'xd.id_pg_metod_payget', 'pm.id')
            ->join('tbl_bar_venue as v', DB::raw("o.id_venue "), 'v.id')
            ->where('o.status', '1')
            ->where('o.id', $id_order)->first();

        $order_details = DB::table('tbl_bar_order_detail as od')
            ->select(['od.id as id_order_detail', 'od.id_jenis_tiket', 't.nama_tiket', 'od.full_name', 'od.email', 'od.phone', 'od.qty', 'od.price'])
            ->join('tbl_bar_jenis_tiket as t', 'od.id_jenis_tiket', 't.id')
            ->where('od.id_order', $id_order)
            ->orderBy('t.nama_tiket')
            ->get();

        $order_tickets = $order_details->groupBy('id_jenis_tiket')->map(fn ($group) => [
                'id_jenis_tiket' => $group->first()->id_jenis_tiket,
                'id_order_detail' => $group->first()->id_order_detail,
                'nama_tiket' => $group->first()->nama_tiket,
                'qty' => $group->sum('qty'),
                'price' => $group->first()->price,
                'full_name' => $group->first()->full_name,
                'email' => $group->first()->email,
                'phone' => $group->first()->phone,
            ]);

        if (!$order) {
            abort(404);
        }
        return view('vip.order.success-payment', [
            'order' => $order,
            'order_details' => $order_details,
            'order_tickets' => $order_tickets,
        ]);
    }


    public function paymentDownloader($id_order)
    {
        // Ambil data order + payment
        $order = DB::table('tbl_bar_order as o')
            ->select([
                'v.nama as venue_name',
                'v.banner as venue_banner',
                'v.alamat as venue_address',
                'o.id as id_order',
                'o.invoice_number',
                'o.date',
                'xd.expires_at',
                'pm.payment_name',
                'pm.img',
                'xd.va_number',
                'o.total_amount',
                'o.currency',
                'o.total_price',
                'o.fees',
                'o.status',
                'xd.qr_string',
                'xd.desktop_web_checkout_url',
                'xd.id_pg_metod_payget',
                'xd.mobile_web_checkout_url',
                'xd.mobile_deeplink_checkout_url'
            ])
            ->leftJoin('tbl_pgxd_payment_method_request as xd', 'o.id', '=', 'xd.order_id')
            ->leftJoin('tbl_pg_metod_payget as pm', 'xd.id_pg_metod_payget', '=', 'pm.id')
            ->leftJoin('tbl_bar_venue as v', 'o.id_venue', '=', 'v.id')
            ->where('o.status', '1')
            ->where('o.id', $id_order)
            ->first();

        // Abort jika order tidak ditemukan
        if (!$order) {
            abort(404);
        }

        // Ambil detail tiket (boleh kosong)
        $order_details = DB::table('tbl_bar_order_detail as od')
            ->select([
                'od.id as id_order_detail',
                'od.id_jenis_tiket',
                't.nama_tiket',
                'od.full_name',
                'od.email',
                'od.phone',
                'od.qty',
                'od.price'
            ])
            ->leftJoin('tbl_bar_jenis_tiket as t', 'od.id_jenis_tiket', '=', 't.id')
            ->where('od.id_order', $id_order)
            ->orderBy('t.nama_tiket')
            ->get();

        // Group jika tidak kosong
        $order_details = $order_details->isNotEmpty() ? $order_details->groupBy('id_jenis_tiket')->map(function ($group) {
            return [
                'id_jenis_tiket' => $group->first()->id_jenis_tiket,
                'id_order_detail' => $group->first()->id_order_detail,
                'nama_tiket' => $group->first()->nama_tiket ?? '-',
                'qty' => $group->sum('qty'),
                'price' => $group->first()->price,
                'full_name' => $group->first()->full_name,
                'email' => $group->first()->email,
                'phone' => $group->first()->phone,
            ];
        }) : collect();

        return view('vip.order.ticketDownloder', [
            'order' => $order,
            'order_details' => $order_details,
        ]);
    }


    public function expiredPayment($id_order = null)
    {
        return view('vip.order.expired-payment', [
            'id_order' => $id_order,
        ]);
    }

    public function failedPayment($id_order = null)
    {
        return view('vip.order.failed-payment', [
            'id_order' => $id_order,
        ]);
    }

    public function checkStatusPayment($id_order)
    {
        try {
            $order = DB::table('tbl_bar_order as o')
                ->select(['o.id as id_order', 'o.status'])
                ->where('o.id', $id_order)->first();

            $response = ApiHelper::successRes2([
                "data" => [
                    'status' => $order->status,
                ],
            ]);
            return $response;

        } catch (Exception $e) {
            $error = [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'rc' => 500,
            ];
            return ApiHelper::errorRes2($error);
        }

    }

    public function resetAllTicketStock(): JsonResponse
    {
        DB::update("UPDATE tbl_bar_jenis_tiket AS t
            LEFT JOIN (
                SELECT id_jenis_tiket, stok
                FROM tbl_bar_jenis_tiket_harga
                WHERE (tanggal = CURDATE() AND hari IS NULL) OR (tanggal IS NULL AND hari = DAYOFWEEK(CURDATE()))
            ) AS h ON h.id_jenis_tiket = t.id
            SET t.stok_tersedia = COALESCE(h.stok, t.default_stok)
        ");

        return ApiHelper::successRes2([
            "data" => [
                'message' => 'Stock berhasil direset',
            ],
        ]);
    }
}
