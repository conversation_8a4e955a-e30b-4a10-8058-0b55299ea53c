@extends('vip.app')
@section('title', 'Booking Checkout - KREEN VIP')

@push('styles')
    <style>
        .sticky-card {
            position: sticky;
            top: 10rem;
            z-index: 10;
        }

        .booking-container {
            position: relative;
            overflow: visible;
        }

        .booking-grid {
            position: relative;
            overflow: visible;
        }

        /* Full width section */
        .full-width-section {
            width: 100vw;
            margin-left: calc(-50vw + 50%);
            padding-left: calc(50vw - 50%);
            padding-right: calc(50vw - 50%);
        }

        /* Header with background image */
        .header-bg {
            background-image: url('https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');
            background-size: cover;
            background-position: center;
            position: relative;
        }

        .header-bg::before {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5));
            z-index: 1;
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        /* Mobile sticky adjustments */
        @media (max-width: 1024px) {
            .sticky-card {
                position: relative;
                top: auto;
            }
        }

        /* Remove any bottom margins/padding */
        .full-width-section:last-child {
            margin-bottom: 0 !important;
            padding-bottom: 0 !important;
        }

        body {
            margin-bottom: 0 !important;
        }

        /* Ensure the main content area has no bottom spacing */
        .explore-nights-bg-container:last-child {
            margin-bottom: 0 !important;
            padding-bottom: 0 !important;
        }

        /* Progress indicator responsive design */
        @media (max-width: 768px) {
            .booking-progress {
                display: none !important;
            }

            /* Show mobile version */
            .booking-progress-mobile {
                display: flex !important;
                justify-content: center;
                padding: 0.5rem 0;
                border-top: 1px solid #374151;
                margin-top: 1rem;
            }
        }

        @media (min-width: 769px) {
            .booking-progress-mobile {
                display: none !important;
            }
        }

        /* Custom Payment Form Styles */
        .payment-custom-form {
            display: none;
            margin-top: 1rem;
            padding: 1rem;
            background: transparent;
        }

        .payment-custom-form.active {
            display: block;
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .custom-form-field {
            margin-bottom: 1rem;
        }

        .custom-form-field:last-child {
            margin-bottom: 0;
        }

        .custom-form-input {
            width: 100%;
            background: transparent;
            border: 2px solid;
            color: white;
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
        }

        .custom-form-input:focus {
            outline: none;
            ring: 2px;
            ring-color: #d4af37;
            border-color: #d4af37;
        }

        .custom-form-input::placeholder {
            color: #9ca3af;
        }

        .custom-form-label {
            display: block;
            color: white;
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        @media (max-width: 640px) {
            .form-row {
                grid-template-columns: 1fr;
            }
        }

        /* Validation Styles */
        .error-message {
            color: #ef4444;
            font-size: 0.75rem;
            margin-top: 0.25rem;
            display: none;
        }

        .error-message.show {
            display: block;
        }

        .input-error {
            border-color: #ef4444 !important;
        }

        .input-success {
            border-color: #10b981 !important;
        }

        .validation-icon {
            position: absolute;
            right: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            width: 1rem;
            height: 1rem;
        }

        .field-container {
            position: relative;
        }
    </style>
@endpush

@section('content')
    <!-- Full Width Header with Background Image -->
    <section class="full-width-section header-bg py-16">
        <div class="header-content text-center">
            <h1 class="text-4xl md:text-6xl font-bold text-white mb-4">{{ $venue->nama }}</h1>
            <p class="text-gray-300 text-lg">{{ $venue->alamat }}</p>
        </div>
    </section>

    <!-- Full Width Personal Information Section -->
    <section class="full-width-section py-16 bg-gradient-to-b from-black to-gray-900">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-12 gap-12 booking-grid">
                <!-- Left Side - Form (8 columns) -->
                <div class="lg:col-span-8 space-y-8">
                    <!-- Personal Information -->
                    <div class="data-diri">
                        <h2 class="text-white text-2xl font-bold mb-6">Fill Personal Information</h2>
                        <div class="space-y-6">
                            @php
                                $guest_count = 1;
                            @endphp
                            @foreach ($tickets_data as $idx_ticket => $ticket)
                                <div class="space-y-6">
                                    @foreach (range(1, $ticket['qty']) as $idx_guest => $guest)
                                        <div class="">
                                            <input type="hidden" name="id_ticket[]" value="{{ $ticket['id'] }}">
                                            <!-- Guest Detail Info -->
                                            <div class="mb-6">
                                                <p class="text-gray-400 text-sm mb-1">Guest Data {{ $guest_count }}</p>
                                                <p class="text-white font-medium text-lg">{{ $ticket['nama_tiket'] }}</p>
                                            </div>
                                            <!-- Form Fields -->
                                            <div class="space-y-6">
                                                <!-- Full Name -->
                                                <div class="field-container">
                                                    <label class="block text-white text-sm font-medium mb-2">
                                                        Full Name <span class="text-red-500">*</span>
                                                    </label>
                                                    <input type="text" name="full_name[]"
                                                        id="full_name_{{ $guest_count }}"
                                                        placeholder="Enter your full name"
                                                        class="w-full bg-transparent border-2 border-kreen-gold text-white px-4 py-4 rounded-lg focus:outline-none focus:ring-2 focus:ring-kreen-gold placeholder-gray-400"
                                                        data-validation="required|min:2|max:100"
                                                        data-guest="{{ $guest_count }}">
                                                    <div class="error-message" id="error_full_name_{{ $guest_count }}">
                                                    </div>
                                                </div>
                                                <!-- Email -->
                                                <div class="field-container">
                                                    <label class="block text-white text-sm font-medium mb-2">
                                                        Email <span class="text-red-500">*</span>
                                                    </label>
                                                    <input type="email" name="email[]" id="email_{{ $guest_count }}"
                                                        placeholder="Enter your email address"
                                                        class="w-full bg-transparent border-2 border-kreen-gold text-white px-4 py-4 rounded-lg focus:outline-none focus:ring-2 focus:ring-kreen-gold placeholder-gray-400"
                                                        data-validation="required|email" data-guest="{{ $guest_count }}">
                                                    <div class="error-message" id="error_email_{{ $guest_count }}"></div>
                                                </div>
                                                <!-- Phone Number -->
                                                <div class="field-container">
                                                    <label class="block text-white text-sm font-medium mb-2">
                                                        Phone Number <span class="text-red-500">*</span>
                                                    </label>
                                                    <input type="number" name="phone_number[]"
                                                        id="phone_number_{{ $guest_count }}"
                                                        placeholder="Enter your phone number"
                                                        class="w-full bg-transparent border-2 border-kreen-gold text-white px-4 py-4 rounded-lg focus:outline-none focus:ring-2 focus:ring-kreen-gold placeholder-gray-400"
                                                        data-validation="required|phone" data-guest="{{ $guest_count }}">
                                                    <div class="error-message" id="error_phone_number_{{ $guest_count }}">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        @php
                                            $guest_count++;
                                        @endphp
                                    @endforeach
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <!-- Payment Method -->
                    <div>
                        <h2 class="text-white text-2xl font-bold mb-6">Choose Payment Method</h2>
                        <div class="space-y-6">
                            @foreach ($payment_methods as $category_name => $payment_method)
                                <div>
                                    <h3 class="text-white text-lg font-semibold mb-4">{{ $category_name }}</h3>
                                    <div class="space-y-3">
                                        @foreach ($payment_method as $payment)
                                            <div>
                                                <label
                                                    class="flex items-center p-3 rounded-lg cursor-pointer hover:border-kreen-gold transition-colors"
                                                    id="payment_label_{{ $payment->id }}">
                                                    <input type="radio" data-fee="{{ $payment->fee }}"
                                                        data-fee-percent="{{ $payment->fee_percent }}"
                                                        data-ppn="{{ $payment->ppn }}"
                                                        data-attributes="{{ $payment->attribute ?? '[]' }}"
                                                        name="payment_method" value="{{ $payment->id }}"
                                                        class="w-5 h-5 text-gray-400 bg-transparent border-2 border-gray-400 mr-4"
                                                        onchange="handlePaymentMethodChange(this)">
                                                    <img src="{{ asset('image/payment-method/' . $payment->img) }}"
                                                        alt="{{ $payment->payment_name }}"
                                                        class="w-12 h-8 object-contain mr-3">
                                                    <span class="text-white">{{ $payment->payment_name }}</span>
                                                </label>

                                                <!-- Custom Form Container -->
                                                <div id="custom_form_{{ $payment->id }}" class="payment-custom-form">
                                                    <!-- Dynamic form will be inserted here -->
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endforeach
                            <div class="error-message" id="error_payment_method"></div>
                        </div>
                    </div>

                    <!-- Terms & Conditions -->
                    <div>
                        <h2 class="text-white text-2xl font-bold mb-6">Terms & Conditions</h2>
                        <div class="space-y-4">
                            <label class="flex items-start space-x-3 cursor-pointer">
                                <input type="checkbox" id="terms_1"
                                    class="mt-1 w-4 h-4 text-kreen-gold bg-transparent border-2 border-kreen-gold rounded focus:ring-kreen-gold shrink-0"
                                    data-validation="required">
                                <span class="text-gray-300 text-sm">
                                    Saya telah membaca dan menyetujui syarat dan ketentuan yang berlaku dengan total
                                    pembayaran sebesar <span id="total_payment2"></span>
                                </span>
                            </label>
                            <label class="flex items-start space-x-3 cursor-pointer">
                                <input type="checkbox" id="terms_2"
                                    class="mt-1 w-4 h-4 text-kreen-gold bg-transparent border-2 border-kreen-gold rounded focus:ring-kreen-gold"
                                    data-validation="required">
                                <span class="text-gray-300 text-sm">
                                    Saya telah memahami dan menyetujui House Rules dari HELEN'S NIGHT MART GADING SERPONG
                                </span>
                            </label>
                            <label class="flex items-start space-x-3 cursor-pointer">
                                <input type="checkbox" id="terms_3"
                                    class="mt-1 w-4 h-4 text-kreen-gold bg-transparent border-2 border-kreen-gold rounded focus:ring-kreen-gold"
                                    data-validation="required">
                                <span class="text-gray-300 text-sm">
                                    Pembelian reservasi akan dikenai biaya. Saya telah membaca dan menyetujui Ketentuan
                                    Pembatalan.
                                </span>
                            </label>
                            <div class="error-message" id="error_terms"></div>
                        </div>
                    </div>
                </div>

                <!-- Right Side - Sticky Booking Summary (4 columns) -->
                <div class="lg:col-span-4">
                    <div class="sticky-card border-2 border-kreen-gold rounded-xl p-6 space-y-6"
                        style="background: linear-gradient(98.07deg, #000000 0%, #2E2E2E 25.48%, #000000 50.48%, #464646 81.73%, #040404 100%);">
                        <h3 class="text-white text-xl font-bold mb-4">Your Booking</h3>
                        <!-- Venue -->
                        <div class="flex items-center space-x-3">
                            <div class="w-6 h-6 flex items-center justify-center flex-shrink-0">
                                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                                        clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div>
                                <p class="text-white font-medium text-sm">{{ $venue->nama }}</p>
                            </div>
                        </div>
                        <!-- Date -->
                        <div class="flex items-center space-x-3">
                            <div class="w-6 h-6 flex items-center justify-center flex-shrink-0">
                                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                                        clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div>
                                <p class="text-gray-400 text-sm">{{ $date }}</p>
                            </div>
                        </div>
                        <!-- Ticket -->
                        <div class="bg-white/20 bg-opacity-50 rounded-lg p-4 space-y-3">
                            @foreach ($tickets_data as $ticket)
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-6 h-6 flex items-center justify-center flex-shrink-0">
                                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                <path
                                                    d="M2 6a2 2 0 012-2h12a2 2 0 012 2v2a2 2 0 100 4v2a2 2 0 01-2 2H4a2 2 0 01-2-2v-2a2 2 0 100-4V6z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-gray-400 text-sm font-medium">
                                                {{ $ticket['nama_tiket'] }}
                                            </p>
                                            <p class="text-gray-400 text-xs">
                                                {{ $ticket['qty'] }}x Guest
                                            </p>
                                        </div>
                                    </div>
                                    @if (isset($ticket['price']))
                                        <div class="text-right">
                                            <p class="text-white text-sm">
                                                Rp {{ number_format($ticket['price'], 0, ',', '.') }}
                                            </p>
                                        </div>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                        <!-- Divider -->
                        <hr class="border-gray-600">
                        <!-- Payment Details -->
                        <div class="space-y-3">
                            <h4 class="text-white font-semibold">Payment Details</h4>
                            @foreach ($tickets_data as $ticket)
                                <div class="flex justify-between">
                                    <span class="text-gray-400 text-sm">{{ $ticket['nama_tiket'] }}</span>
                                    <span
                                        class="text-white text-sm">{{ App\Helpers\Format::formatCurrency($ticket['total_harga'], 'IDR') }}</span>
                                </div>
                            @endforeach
                            <div class="flex justify-between">
                                <span class="text-gray-400 text-sm">Service Fee</span>
                                <span class="text-white text-sm" id="service_fee"></span>
                            </div>
                            <hr class="border-gray-600">
                            <div class="flex justify-between">
                                <span class="text-white font-medium">Total Payment</span>
                                <span class="text-kreen-gold font-bold text-lg"
                                    id="total_payment">{{ App\Helpers\Format::formatCurrency($tickets_data->sum('total_harga'), 'IDR') }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Full Width Booking Button Section - No bottom padding -->
    <section class="full-width-section bg-gray-900" style="padding-top: 1rem; padding-bottom: 10rem;">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-12 gap-12">
                <div class="lg:col-span-8">
                    <button onclick="bookingNow()"
                        class="w-full bg-kreen-gold hover:bg-yellow-500 text-black font-bold py-4 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 text-lg mb-[50px]">
                        BOOKING NOW
                    </button>
                </div>
                <div class="lg:col-span-4">
                    <!-- Empty space to align with the form -->
                </div>
            </div>
        </div>
    </section>
@endsection

@push('scripts')
    <script>
        // Validation object to store all validation functions
        const BookingValidation = {
            // Validation rules
            rules: {
                required: function(value) {
                    return value.trim() !== '';
                },
                email: function(value) {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    return emailRegex.test(value);
                },
                phone: function(value) {
                    // Indonesian phone number validation (flexible)
                    const phoneRegex = /^(\+62|62|0)[0-9]{8,13}$/;
                    const cleanValue = value.replace(/[\s\-$$$$]/g, '');
                    return phoneRegex.test(cleanValue);
                },
                min: function(value, minLength) {
                    return value.length >= parseInt(minLength);
                },
                max: function(value, maxLength) {
                    return value.length <= parseInt(maxLength);
                },
                cardNumber: function(value) {
                    // Basic card number validation (Luhn algorithm)
                    const cleanValue = value.replace(/\s/g, '');
                    if (cleanValue.length < 13 || cleanValue.length > 19) return false;

                    let sum = 0;
                    let isEven = false;

                    for (let i = cleanValue.length - 1; i >= 0; i--) {
                        let digit = parseInt(cleanValue.charAt(i));

                        if (isEven) {
                            digit *= 2;
                            if (digit > 9) {
                                digit -= 9;
                            }
                        }

                        sum += digit;
                        isEven = !isEven;
                    }

                    return sum % 10 === 0;
                },
                expiryMonth: function(value) {
                    const month = parseInt(value);
                    return month >= 1 && month <= 12;
                },
                expiryYear: function(value) {
                    const year = parseInt(value);
                    const currentYear = new Date().getFullYear();
                    return year >= currentYear && year <= currentYear + 20;
                },
                cvv: function(value) {
                    return /^[0-9]{3,4}$/.test(value);
                }
            },

            // Error messages
            messages: {
                required: 'This field is required',
                email: 'Please enter a valid email address',
                phone: 'Please enter a valid phone number',
                min: 'Minimum {0} characters required',
                max: 'Maximum {0} characters allowed',
                cardNumber: 'Please enter a valid card number',
                expiryMonth: 'Please enter a valid month (1-12)',
                expiryYear: 'Please enter a valid year',
                cvv: 'Please enter a valid CVV'
            },

            // Validate single field
            validateField: function(element) {
                const value = element.value.trim();
                const validationRules = element.getAttribute('data-validation');
                const fieldId = element.id;
                const errorElement = document.getElementById('error_' + fieldId);

                if (!validationRules) return true;

                const rules = validationRules.split('|');
                let isValid = true;
                let errorMessage = '';

                for (let rule of rules) {
                    const [ruleName, ruleValue] = rule.split(':');

                    if (ruleName === 'required' && !this.rules.required(value)) {
                        isValid = false;
                        errorMessage = this.messages.required;
                        break;
                    }

                    if (value !== '' && this.rules[ruleName]) {
                        if (ruleValue) {
                            if (!this.rules[ruleName](value, ruleValue)) {
                                isValid = false;
                                errorMessage = this.messages[ruleName].replace('{0}', ruleValue);
                                break;
                            }
                        } else {
                            if (!this.rules[ruleName](value)) {
                                isValid = false;
                                errorMessage = this.messages[ruleName];
                                break;
                            }
                        }
                    }
                }

                this.showFieldValidation(element, isValid, errorMessage);
                return isValid;
            },

            // Show field validation result
            showFieldValidation: function(element, isValid, errorMessage) {
                const errorElement = document.getElementById('error_' + element.id);

                if (isValid) {
                    element.classList.remove('input-error');
                    element.classList.add('input-success');
                    if (errorElement) {
                        errorElement.classList.remove('show');
                        errorElement.textContent = '';
                    }
                } else {
                    element.classList.remove('input-success');
                    element.classList.add('input-error');
                    if (errorElement) {
                        errorElement.classList.add('show');
                        errorElement.textContent = errorMessage;
                    }
                }
            },

            // Validate all personal information fields
            validatePersonalInfo: function() {
                let isValid = true;
                const personalFields = document.querySelectorAll(
                    'input[name="full_name[]"], input[name="email[]"], input[name="phone_number[]"]');

                personalFields.forEach(field => {
                    if (!this.validateField(field)) {
                        isValid = false;
                    }
                });

                return isValid;
            },

            // Validate payment method selection
            validatePaymentMethod: function() {
                const selectedPayment = document.querySelector('input[name="payment_method"]:checked');
                const errorElement = document.getElementById('error_payment_method');

                if (!selectedPayment) {
                    if (errorElement) {
                        errorElement.classList.add('show');
                        errorElement.textContent = 'Please select a payment method';
                    }
                    return false;
                }

                if (errorElement) {
                    errorElement.classList.remove('show');
                    errorElement.textContent = '';
                }

                // Validate custom payment fields if they exist
                const paymentId = selectedPayment.value;
                const customForm = document.getElementById(`custom_form_${paymentId}`);

                if (customForm && customForm.classList.contains('active')) {
                    const customFields = customForm.querySelectorAll('input[required]');
                    let customValid = true;

                    customFields.forEach(field => {
                        if (!this.validateCustomPaymentField(field)) {
                            customValid = false;
                        }
                    });

                    return customValid;
                }

                return true;
            },

            // Validate custom payment fields
            validateCustomPaymentField: function(element) {
                const value = element.value.trim();
                const fieldCode = element.name.match(/payment_data\[(.+)\]/)[1];
                let isValid = true;
                let errorMessage = '';

                if (value === '') {
                    isValid = false;
                    errorMessage = 'This field is required';
                } else {
                    switch (fieldCode) {
                        case 'card_number':
                            if (!this.rules.cardNumber(value)) {
                                isValid = false;
                                errorMessage = this.messages.cardNumber;
                            }
                            break;
                        case 'expiry_month':
                            if (!this.rules.expiryMonth(value)) {
                                isValid = false;
                                errorMessage = this.messages.expiryMonth;
                            }
                            break;
                        case 'expiry_year':
                            if (!this.rules.expiryYear(value)) {
                                isValid = false;
                                errorMessage = this.messages.expiryYear;
                            }
                            break;
                        case 'cvv':
                            if (!this.rules.cvv(value)) {
                                isValid = false;
                                errorMessage = this.messages.cvv;
                            }
                            break;
                    }
                }

                this.showCustomFieldValidation(element, isValid, errorMessage);
                return isValid;
            },

            // Show custom field validation
            showCustomFieldValidation: function(element, isValid, errorMessage) {
                let errorElement = element.parentNode.querySelector('.error-message');

                if (!errorElement) {
                    errorElement = document.createElement('div');
                    errorElement.className = 'error-message';
                    element.parentNode.appendChild(errorElement);
                }

                if (isValid) {
                    element.classList.remove('input-error');
                    element.classList.add('input-success');
                    errorElement.classList.remove('show');
                    errorElement.textContent = '';
                } else {
                    element.classList.remove('input-success');
                    element.classList.add('input-error');
                    errorElement.classList.add('show');
                    errorElement.textContent = errorMessage;
                }
            },

            // Validate terms and conditions
            validateTerms: function() {
                const termsCheckboxes = document.querySelectorAll('#terms_1, #terms_2, #terms_3');
                const errorElement = document.getElementById('error_terms');
                let allChecked = true;

                termsCheckboxes.forEach(checkbox => {
                    if (!checkbox.checked) {
                        allChecked = false;
                    }
                });

                if (!allChecked) {
                    if (errorElement) {
                        errorElement.classList.add('show');
                        errorElement.textContent = 'Please accept all terms and conditions';
                    }
                    return false;
                }

                if (errorElement) {
                    errorElement.classList.remove('show');
                    errorElement.textContent = '';
                }

                return true;
            },

            // Validate entire form
            validateForm: function() {
                const personalValid = this.validatePersonalInfo();
                const paymentValid = this.validatePaymentMethod();
                const termsValid = this.validateTerms();

                return personalValid && paymentValid && termsValid;
            }
        };

        // Initialize validation when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Add real-time validation to personal info fields
            const personalFields = document.querySelectorAll(
                'input[name="full_name[]"], input[name="email[]"], input[name="phone_number[]"]');
            personalFields.forEach(field => {
                field.addEventListener('blur', function() {
                    BookingValidation.validateField(this);
                });

                field.addEventListener('input', function() {
                    // Clear error state on input
                    this.classList.remove('input-error');
                    const errorElement = document.getElementById('error_' + this.id);
                    if (errorElement) {
                        errorElement.classList.remove('show');
                    }
                });
            });

            // Enhanced sticky behavior
            function handleStickyCard() {
                const stickyCard = document.querySelector('.sticky-card');
                const bookingContainer = document.querySelector('.booking-container');
                if (!stickyCard || !bookingContainer) return;

                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            stickyCard.style.position = 'sticky';
                            stickyCard.style.top = '2rem';
                        }
                    });
                }, {
                    threshold: 0.1
                });

                observer.observe(bookingContainer);
            }

            handleStickyCard();
        });

        // Header Navigation Replacement for Booking Flow
        function replaceHeaderNavigation() {
            // Hide existing navigation
            const desktopNav = document.querySelector('nav.hidden.md\\:flex');
            const loginButton = document.querySelector('a[href=""].hidden.md\\:inline-block');
            if (desktopNav) {
                desktopNav.style.display = 'none';
            }
            if (loginButton) {
                loginButton.style.display = 'none';
            }

            // Create progress indicator
            const headerContainer = document.querySelector('header .flex.items-center.justify-between');
            if (headerContainer && !document.querySelector('.booking-progress')) {
                const progressContainer = document.createElement('div');
                progressContainer.className = 'booking-progress flex items-center space-x-8';
                progressContainer.innerHTML = `
            <div class="flex items-center space-x-2">
                <div class="w-6 h-6 bg-kreen-gold rounded-full flex items-center justify-center">
                    <span class="text-black text-sm font-bold">1</span>
                </div>
                <span class="text-kreen-gold font-medium text-sm">Guest Info</span>
            </div>
            <div class="flex items-center">
                <div class="w-8 h-0.5 bg-gray-600"></div>
                <svg class="w-3 h-3 text-gray-600 mx-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                </svg>
            </div>
            <div class="flex items-center space-x-2">
                <div class="w-6 h-6 border-2 border-gray-600 rounded-full flex items-center justify-center">
                    <span class="text-gray-600 text-sm font-bold">2</span>
                </div>
                <span class="text-gray-600 font-medium text-sm">Payment</span>
            </div>
            <div class="flex items-center">
                <div class="w-8 h-0.5 bg-gray-600"></div>
                <svg class="w-3 h-3 text-gray-600 mx-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                </svg>
            </div>
            <div class="flex items-center space-x-2">
                <div class="w-6 h-6 border-2 border-gray-600 rounded-full flex items-center justify-center">
                    <span class="text-gray-600 text-sm font-bold">3</span>
                </div>
                <span class="text-gray-600 font-medium text-sm">You're In!</span>
            </div>
        `;
                // Insert progress indicator after logo
                const logoContainer = headerContainer.querySelector('.flex.items-center');
                if (logoContainer && logoContainer.nextElementSibling) {
                    headerContainer.insertBefore(progressContainer, logoContainer.nextElementSibling);
                }
            }
        }

        // Initialize header replacement when page loads
        replaceHeaderNavigation();
        // Also handle if header loads after this script
        setTimeout(replaceHeaderNavigation, 100);

        // Add mobile progress indicator
        function addMobileProgress() {
            const mobileMenu = document.querySelector('#mobile-menu');
            if (mobileMenu && !document.querySelector('.booking-progress-mobile')) {
                const mobileProgress = document.createElement('div');
                mobileProgress.className =
                    'booking-progress-mobile md:hidden flex items-center justify-center space-x-4 px-4 py-3 bg-kreen-dark border-t border-gray-800';
                mobileProgress.innerHTML = `
            <div class="flex items-center space-x-1">
                <div class="w-5 h-5 bg-kreen-gold rounded-full flex items-center justify-center">
                    <span class="text-black text-xs font-bold">1</span>
                </div>
                <span class="text-kreen-gold font-medium text-[10px] md:text-xs">Info</span>
            </div>
            <svg class="w-3 h-3 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            <div class="flex items-center space-x-1">
                <div class="w-5 h-5 border border-gray-600 rounded-full flex items-center justify-center">
                    <span class="text-gray-600 text-xs font-bold">2</span>
                </div>
                <span class="text-gray-600 font-medium text-[10px] md:text-xs">Payment</span>
            </div>
            <svg class="w-3 h-3 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>
            <div class="flex items-center space-x-1">
                <div class="w-5 h-5 border border-gray-600 rounded-full flex items-center justify-center">
                    <span class="text-gray-600 text-xs font-bold">3</span>
                </div>
                <span class="text-gray-600 font-medium text-[10px] md:text-xs">In!</span>
            </div>
        `;
                mobileMenu.parentNode.insertBefore(mobileProgress, mobileMenu.nextSibling);
            }
        }

        // Add mobile progress
        addMobileProgress();
        setTimeout(addMobileProgress, 100);

        // Handle Payment Method Change with Custom Forms
        function handlePaymentMethodChange(element) {
            // Remove active state from all payment options
            document.querySelectorAll('label[id^="payment_label_"]').forEach(label => {
                label.classList.remove('border-kreen-gold', 'bg-kreen-gold', 'bg-opacity-10');
                label.classList.add('border-gray-600');
            });

            // Hide all custom forms
            document.querySelectorAll('.payment-custom-form').forEach(form => {
                form.classList.remove('active');
            });

            if (element.checked) {
                // Add active state to selected payment option
                const label = element.closest('label');
                label.classList.remove('border-gray-600');
                label.classList.add('bg-opacity-10');

                // Get payment method data
                const paymentMethodId = element.value;
                const fee = element.getAttribute('data-fee');
                const feePercent = element.getAttribute('data-fee-percent');
                const ppn = element.getAttribute('data-ppn');
                const attributes = element.getAttribute('data-attributes');

                // Calculate service fee
                calculateServiceFee(fee, feePercent, ppn);

                // Show custom form if attributes exist
                if (attributes && attributes !== '[]') {
                    showCustomForm(paymentMethodId, attributes);
                }
            }
        }

        // Show Custom Form based on attributes
        function showCustomForm(paymentMethodId, attributesJson) {
            try {
                const attributes = JSON.parse(attributesJson);
                const formContainer = document.getElementById(`custom_form_${paymentMethodId}`);

                if (!formContainer || !attributes.length) return;

                // Generate form HTML
                let formHTML = '<h4 class="text-white font-semibold mb-4">Payment Information</h4>';

                // Group fields for better layout (e.g., expiry month/year in same row)
                const groupedFields = groupFormFields(attributes);

                groupedFields.forEach(group => {
                    if (group.length === 1) {
                        // Single field
                        const field = group[0];
                        formHTML += generateFieldHTML(field, paymentMethodId);
                    } else {
                        // Multiple fields in row
                        formHTML += '<div class="form-row">';
                        group.forEach(field => {
                            formHTML += generateFieldHTML(field, paymentMethodId, true);
                        });
                        formHTML += '</div>';
                    }
                });

                formContainer.innerHTML = formHTML;
                formContainer.classList.add('active');

                // Add event listeners for validation
                addFormValidation(paymentMethodId, attributes);
            } catch (error) {
                console.error('Error parsing payment attributes:', error);
            }
        }

        // Group form fields for better layout
        function groupFormFields(attributes) {
            const groups = [];
            let currentGroup = [];

            attributes.forEach((field, index) => {
                // Group expiry month and year together
                if (field.code === 'expiry_month') {
                    currentGroup = [field];
                } else if (field.code === 'expiry_year' && currentGroup.length > 0 && currentGroup[0].code ===
                    'expiry_month') {
                    currentGroup.push(field);
                    groups.push([...currentGroup]);
                    currentGroup = [];
                } else {
                    if (currentGroup.length > 0) {
                        groups.push([...currentGroup]);
                        currentGroup = [];
                    }
                    groups.push([field]);
                }
            });

            if (currentGroup.length > 0) {
                groups.push(currentGroup);
            }

            return groups;
        }

        // Generate HTML for form field
        function generateFieldHTML(field, paymentMethodId, isInRow = false) {
            const fieldId = `${field.code}_${paymentMethodId}`;
            const containerClass = isInRow ? 'custom-form-field' : 'custom-form-field';

            let inputType = field.type;
            let placeholder = `Enter ${field.name.toLowerCase()}`;
            let maxLength = '';
            let pattern = '';

            // Customize input based on field type
            switch (field.code) {
                case 'card_number':
                    // maxLength = 'maxlength="19"';
                    // placeholder = '1234 5678 9012 3456';
                    // pattern = 'pattern="[0-9\\s]{13,19}"';
                    // break;
                    inputType = 'text'; // ⬅️ Force jadi text
                    maxLength = 'maxlength="19"';
                    placeholder = '1234 5678 9012 3456';
                    pattern = 'pattern="[0-9\\s]{13,19}"';
                    break;
                case 'expiry_month':
                    maxLength = 'maxlength="2"';
                    placeholder = 'MM';
                    pattern = 'pattern="[0-9]{1,2}"';
                    break;
                case 'expiry_year':
                    maxLength = 'maxlength="4"';
                    placeholder = 'YYYY';
                    pattern = 'pattern="[0-9]{4}"';
                    break;
                case 'cvv':
                    maxLength = 'maxlength="4"';
                    placeholder = '123';
                    pattern = 'pattern="[0-9]{3,4}"';
                    break;
            }

            return `
                <div class="${containerClass}">
                    <label for="${fieldId}" class="custom-form-label">
                        ${field.name} <span class="text-red-500">*</span>
                    </label>
                    <input type="${inputType}"
                           id="${fieldId}"
                           name="payment_data[${field.code}]"
                           placeholder="${placeholder}"
                           class="custom-form-input"
                           ${maxLength}
                           ${pattern}
                           required>
                </div>
            `;
        }

        // Add form validation
        function addFormValidation(paymentMethodId, attributes) {
            attributes.forEach(field => {
                const fieldId = `${field.code}_${paymentMethodId}`;
                const input = document.getElementById(fieldId);

                if (!input) return;

                // Add blur validation
                input.addEventListener('blur', function() {
                    BookingValidation.validateCustomPaymentField(this);
                });

                // Add input formatting and validation
                input.addEventListener('input', function(e) {
                    // Clear error state on input
                    e.target.classList.remove('input-error');
                    const errorElement = e.target.parentNode.querySelector('.error-message');
                    if (errorElement) {
                        errorElement.classList.remove('show');
                    }

                    // Add specific validation based on field type
                    switch (field.code) {
                        case 'card_number':
                            // Format card number with spaces
                            let value = e.target.value.replace(/\D/g, '').substring(0, 16);
                            let formattedValue = value.replace(/(.{4})/g, '$1 ').trim();
                            if (formattedValue.length > 16) {
                                formattedValue = formattedValue.substring(0, 19);
                            }
                            e.target.value = formattedValue;
                            break;

                        case 'expiry_month':
                            let monthValue = e.target.value.replace(/\D/g, '');
                            if (monthValue.length >= 1) {
                                let month = parseInt(monthValue);
                                if (month > 12) {
                                    monthValue = '12';
                                } else if (month < 1 && monthValue.length === 2) {
                                    monthValue = '01';
                                }
                            }
                            e.target.value = monthValue;
                            break;

                        case 'expiry_year':
                            let yearValue = e.target.value.replace(/\D/g, '');
                            e.target.value = yearValue;
                            break;

                        case 'cvv':
                            let cvvValue = e.target.value.replace(/\D/g, '');
                            e.target.value = cvvValue;
                            break;
                    }
                });
            });
        }

        // Calculate service fee
        function calculateServiceFee(fee, feePercent, ppn) {
            const totalPayment = @json($tickets_data->sum('total_harga'));
            let serviceFee = ((totalPayment / (1 - (feePercent / 100) * (1 + ppn / 100)) + fee * (1 + ppn / 100)) -
                totalPayment).toFixed(5);

            const currency = @json($venue->currency);
            if (currency !== 'IDR') {
                serviceFee += totalPayment * 1 / 100;
                serviceFee = Math.ceil(serviceFee * 100) / 100;
            } else {
                serviceFee = Math.ceil(serviceFee);
            }

            $('#service_fee').text(formatCurrency(serviceFee, currency).result);
            $('#total_payment').text(formatCurrency(totalPayment + parseFloat(serviceFee), currency).result);
            $('#total_payment2').text(formatCurrency(totalPayment + parseFloat(serviceFee), currency).result);
        }

        // Enhanced booking function with comprehensive validation
        function bookingNow() {
            // Validate entire form before proceeding
            if (!BookingValidation.validateForm()) {
                // Scroll to first error
                const firstError = document.querySelector('.input-error, .error-message.show');
                if (firstError) {
                    firstError.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });
                }
                return;
            }

            const tickets = @json($tickets_data);
            const guestInfos = [];
            const ticketData = [];

            // Get guest information
            const fullNameInputs = document.querySelectorAll('input[name="full_name[]"]');
            const emailInputs = document.querySelectorAll('input[name="email[]"]');
            const phoneNumberInputs = document.querySelectorAll('input[name="phone_number[]"]');
            const idTicketInputs = document.querySelectorAll('input[name="id_ticket[]"]');

            fullNameInputs.forEach((input, index) => {
                guestInfos.push({
                    id_ticket: idTicketInputs[index].value,
                    full_name: input.value,
                    email: emailInputs[index].value,
                    phone_number: phoneNumberInputs[index].value,
                });
            });

            tickets.forEach((ticket, index) => {
                const guestInfoData = guestInfos.filter((guestInfo) => guestInfo.id_ticket == ticket.id);
                ticketData.push({
                    id: ticket.id,
                    qty: ticket.qty,
                    guest_infos: guestInfoData.map((guestInfo) => ({
                        full_name: guestInfo.full_name,
                        email: guestInfo.email,
                        phone_number: guestInfo.phone_number,
                    })),
                });
            });

            // Get payment method and custom data
            const selectedPaymentMethod = document.querySelector('input[name="payment_method"]:checked');
            let paymentMethod = {
                id: selectedPaymentMethod.value,
            };

            // Get custom payment data if exists
            const customPaymentData = {};
            const customFormInputs = document.querySelectorAll('input[name^="payment_data["]');
            customFormInputs.forEach(input => {
                if (input.value.trim() !== '') {
                    const fieldName = input.name.match(/payment_data\[(.+)\]/)[1];
                    customPaymentData[fieldName] = input.value.trim();
                }
            });

            if (Object.keys(customPaymentData).length > 0) {
                paymentMethod = {
                    ...paymentMethod,
                    ...customPaymentData
                }
            }

            const orderData = {
                _token: @json(csrf_token()),
                id_venue: @json($venue->id),
                date: @json(date('Y-m-d', strtotime($date))),
                tickets: JSON.stringify(ticketData),
                payment_method: JSON.stringify(paymentMethod),
            };

            console.log('Order Data:', orderData);

            // Disable button to prevent double submission
            const bookingButton = document.querySelector('button[onclick="bookingNow()"]');
            const originalText = bookingButton.textContent;
            bookingButton.disabled = true;
            bookingButton.textContent = 'Processing...';

            $.ajax({
                url: `{{ route('order.submitGuestInfo') }}`,
                type: 'POST',
                dataType: 'json',
                data: orderData,
                success: function(response) {
                    if (response.success) {
                        const orderPaymentBaseUrl =
                            "{{ route('order.waitingPayment', ['id_order' => ':id_order']) }}";
                        const redirectUrl = orderPaymentBaseUrl.replace(':id_order', response.data.id_order);
                        window.location.href = redirectUrl;
                    } else {
                        alert(response.message);
                        // Re-enable button
                        bookingButton.disabled = false;
                        bookingButton.textContent = originalText;
                    }
                },
                error: function(xhr, status, error) {
                    console.log('Error:', error);
                    alert('An error occurred while processing your booking. Please try again.');
                    // Re-enable button
                    bookingButton.disabled = false;
                    bookingButton.textContent = originalText;
                },
            });
        }
    </script>
@endpush
