<!DOCTYPE html>
<html lang="id" style="font-size: 14px;">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email</title>
    <style>
        @media print {
            #btn-join {
                display: none;
            }
        }
    </style>
</head>

<body
    style="line-height: 1.5; font-family: ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'; margin-left: auto; margin-right: auto; padding: 0; text-wrap: break-word;">
    <!-- Ringkas: hanya pakai data yang tersedia -->
    <div style="background-color:#f5f7fa;padding:2rem 1rem;font-family:sans-serif;">
        <center>
            <div style="max-width:600px;background:white;border-radius:10px;padding:1.5rem;">
                <div
                    style="background-color:#000;width:fit-content;padding:0.5rem;border-radius:10px;margin-bottom:0 auto 1rem auto;">
                    <img src="https://dev.kreenvip.com/image/kreenvip/kreenvip.png" style="width:140px;"
                        alt="Kreen Logo">
                </div>

                <h2 style="color:#000;">E-Ticket</h2>
                <p style="color:#808080;">Order ID: {{ $data['order']['id_order'] }}</p>
                <p style="color:#E01A21;">Tanggal Order:
                    {{ \Carbon\Carbon::parse($data['order']['date'])->translatedFormat('d F Y') }}</p>

                <hr style="margin: 1.5rem 0;">

                @foreach ($data['ticket'] as $ticketId => $ticket)
                    <h3 style="margin-top:1rem;">{{ $ticket['name_ticket'] }}</h3>

                    @foreach ($data['order_detail'][$ticketId] as $ticketCode => $detail)
                        <div
                            style="border:1px solid #eee;border-radius:8px;padding:1rem;margin-top:1rem;text-align:left;">
                            <div style="display:flex;">
                                <div style="width:120px;">
                                    <img src="https://api.qrserver.com/v1/create-qr-code?size=140x140&data={{ $ticketCode }}"
                                        alt="QR Code" style="width:100%;">
                                </div>
                                <div style="padding-left:1rem;">
                                    <p style="margin:0;font-weight:bold;font-size:16px;">{{ $detail['fullname'] }}</p>
                                    <p style="margin:0;color:#E01A21;">{{ $detail['email'] }}</p>
                                    <p style="margin-top:8px;color:#808080;">Ticket ID: {{ $ticketCode }}</p>
                                </div>
                            </div>
                        </div>
                    @endforeach
                @endforeach

                <hr style="margin: 2rem 0;">

                <h4>Venue</h4>
                <p style="margin:0;font-weight:bold;">{{ $data['event']['nama_venue'] }}</p>
                <p style="margin:0;color:#808080;">{{ $data['event']['alamat'] }}</p>

                <p style="margin-top:2rem;color:#888;">Terima kasih telah memesan tiket melalui Kreen VIP.</p>
            </div>
        </center>
    </div>
    <script>
        const type = `{{ request()->get('type') }}`
        if (type == 'download') {
            window.print();
        }
    </script>
</body>

</html>
