<?php

namespace App\Http\Controllers;

use App\Helpers\Ticket;
use App\Helpers\ApiHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class VenueController extends Controller
{
    public function exploreNights() {
        $venue_cities = DB::select("SELECT DISTINCT v.id_kota as id, ar.name
        FROM tbl_bar_venue as v
        JOIN kreen_production_online.tbl_adm_regency as ar ON v.id_kota  = ar.id
        WHERE v.flag_aktif = '1'
        ORDER BY ar.name");
        return view('vip.venue.explore_nights', compact('venue_cities'));
    }

    public function detailVenue($slug)
    {
        $venue_data = DB::select("SELECT v.id,v.nama,v.flag_sale,v.informasi,v.banner,v.lat,v.lng,v.alamat,v.slug, v.username_ig, v.no_wa, o.hari, o.jam_buka, o.jam_tutup FROM tbl_bar_venue v LEFT JOIN tbl_bar_venue_open_hour o ON v.id = o.id_venue WHERE v.slug = '$slug' AND v.flag_aktif = '1'");
        $open_hour = collect($venue_data)->whereNotNull('hari')->whereNotNull('jam_buka')->whereNotNull('jam_tutup')->map(fn ($item) => [
            'hari' => $item->hari,
            'jam_buka' => $item->jam_buka,
            'jam_tutup' => $item->jam_tutup,
        ]);

        $venue = [
            'id' => $venue_data[0]->id,
            'nama' => $venue_data[0]->nama,
            'slug' => $venue_data[0]->slug,
            'informasi' => $venue_data[0]->informasi,
            'lat' => $venue_data[0]->lat,
            'lng' => $venue_data[0]->lng,
            'alamat' => $venue_data[0]->alamat,
            'banner' => $venue_data[0]->banner,
            'open_hour' => $open_hour,
            'flag_sale' => $venue_data[0]->flag_sale,
            'username_ig' => $venue_data[0]->username_ig,
            'no_wa' => $venue_data[0]->no_wa,
        ];

        $galleries = DB::select("SELECT g.id, g.gambar FROM tbl_bar_venue_galeri g WHERE g.id_venue = '{$venue_data[0]->id}' AND g.flag_aktif = '1'");

        $today = date('Y-m-d');

        $upcoming_events = DB::table('tbl_bar_event')
            ->select('id', 'id_venue', 'nama', 'tanggal', 'lokasi', 'gambar')
            ->where('id_venue', $venue_data[0]->id)
            ->where('tanggal', '>=', $today)
            ->where('flag_aktif', '1')
            ->orderBy('tanggal', 'asc')
            ->limit(1)
            ->first();

        return view('vip.venue.detail_venue', compact('venue', 'open_hour', 'galleries', 'upcoming_events'));
    }

    public function ajaxGetTickets(Request $request) {
        $venue_id = $request->input('venue_id');
        $date = $request->input('date') ?? date('Y-m-d');
        $date = date('Y-m-d', strtotime($date));
        $date_index = (int) date('N', strtotime($date));

        $tickets = Ticket::getTicketsByVenueIdAndDate($venue_id, $date);

        $view = view('vip.venue.ajax.list_ticket', compact('tickets'))->render();
        return $view;
    }
}
