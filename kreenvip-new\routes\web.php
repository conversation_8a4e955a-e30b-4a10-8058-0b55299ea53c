<?php


use App\Http\Controllers\Admin\DashboardController;

use App\Helpers\Email;

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DjController;
use App\Http\Controllers\TosController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\EventController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\VenueController;
use App\Http\Controllers\XenditController;
use App\Http\Controllers\MyOrderController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\Auth\VerificationController;
use App\Http\Controllers\Auth\ResetPasswordController;
use App\Http\Controllers\Auth\ForgotPasswordController;
use App\Http\Controllers\Admin\EventsController;
use App\Http\Controllers\Admin\VenueController as AdminVenueController;

use App\Http\Controllers\Admin\EventVenueImportController;
use App\Http\Controllers\Admin\ExcelTemplateGeneratorController;

Route::get('/term-and-condition', [TosController::class, 'index'])->name('tos');
Route::get('/privacy-policy', [TosController::class, 'privacy'])->name('privacy');
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/ajax/explore_nights', [HomeController::class, 'ajaxExploreNights'])->name('ajax.exploreNights');
Route::get('/ajax/explore_events', [HomeController::class, 'ajaxExploreEvents'])->name('ajax.exploreEvents');
Route::get('/detail', [HomeController::class, 'detail_bar'])->name('detail.nights');

Route::get('/data', [HomeController::class, 'data'])->name('data.nights');
Route::get('/data/payments', [HomeController::class, 'payments'])->name('payments.nights');
Route::get('/ticket', [HomeController::class, 'ticket'])->name('ticket.nights');


Route::name('auth.')->prefix('auth')->group(function () {
    Route::get('login', [LoginController::class, 'login'])->name('login')->middleware('guest');
    Route::post('login', [LoginController::class, 'submitLogin'])->name('submitLogin')->middleware('guest');
    Route::post('logout', [LoginController::class, 'logout'])->name('logout');

    Route::get('google/redirect', [LoginController::class, 'googleRedirect'])->name('googleRedirect');
    Route::get('google/callback', [LoginController::class, 'googleCallback'])->name('googleCallback');

    Route::get('register', [RegisterController::class, 'register'])->name('register')->middleware('guest');
    Route::post('register', [RegisterController::class, 'submitRegister'])->name('submitRegister')->middleware('guest');

    Route::name('forgotPassword.')->prefix('forgot-password')->group(function () {
        Route::get('/', [ForgotPasswordController::class, 'index'])->name('index')->middleware('guest');
        Route::post('/', [ForgotPasswordController::class, 'process'])->name('process')->middleware('guest');
        Route::get('/success-send', [ForgotPasswordController::class, 'successSend'])->name('successSend')->middleware('guest');
        Route::get('/reset', [ForgotPasswordController::class, 'reset'])->name('reset')->middleware('guest');
        Route::post('/reset', [ForgotPasswordController::class, 'resetProcess'])->name('resetProcess')->middleware('guest');
        Route::get('/success-reset', [ForgotPasswordController::class, 'successReset'])->name('successReset')->middleware('guest');
    });
    Route::name('verificationEmail.')->prefix('verification-email')->group(function () {
        Route::get('send-email', [VerificationController::class, 'sendEmail'])->name('sendEmail')->middleware('auth');
        Route::get('verify', [VerificationController::class, 'verify'])->name('verify')->middleware('auth');
        Route::get('success', [VerificationController::class, 'success'])->name('success')->middleware('auth');
    });
});

// Route::get('/auth/login', [LoginController::class, 'login'])->name('login')->middleware('guest');

// Home
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/ajax/explore_nights', [HomeController::class, 'ajaxExploreNights'])->name('ajax.exploreNights');
Route::get('/ajax/explore_events', [HomeController::class, 'ajaxExploreEvents'])->name('ajax.exploreEvents');

Route::get('/data/payments', [HomeController::class, 'payments'])->name('payments.nights');
Route::get('/ticket', [HomeController::class, 'ticket'])->name('ticket.nights');

// Event
Route::prefix('event')->name('event.')->group(function () {
    Route::get('/explore', [EventController::class, 'exploreEvents'])->name('exploreEvents');
    Route::get('{slug}', [EventController::class, 'detailEvent'])->name('detailEvent');
});
// Venue
Route::prefix('venue')->name('venue.')->group(function () {
    Route::get('/explore', [VenueController::class, 'exploreNights'])->name('exploreNights');
    Route::get('{slug}', [VenueController::class, 'detailVenue'])->name('detailVenue');
    Route::get('ajax/get_tickets', [VenueController::class, 'ajaxGetTickets'])->name('ajax.getTickets');
});

// // My Order Page
// Route::get('/my-order', [MyOrderController::class, 'index'])->name('order.myOrder');
// Route::get('/my-order/detail', [MyOrderController::class, 'detail'])->name('order.myOrderDetail');
Route::prefix('order')->name('order.')->group(function () {
    Route::post('guest-info', [OrderController::class, 'guestInfo'])->name('guestInfo');
    Route::post('submit-guest-info', [OrderController::class, 'submitGuestInfo'])->name('submitGuestInfo');
    Route::get('waiting-payment/{id_order}', [OrderController::class, 'waitingPayment'])->name('waitingPayment');
    Route::get('success-payment/{id_order}', [OrderController::class, 'successPayment'])->name('successPayment');
    Route::get('my-ticket/{id_order}', [OrderController::class, 'paymentDownloader'])->name('paymentDownloader');
    Route::get('expired-payment/{id_order}', [OrderController::class, 'expiredPayment'])->name('expiredPayment');
    Route::get('failed-payment/{id_order}', [OrderController::class, 'failedPayment'])->name('failedPayment');
    Route::get('check-status-payment/{id_order}', [OrderController::class, 'checkStatusPayment'])->name('checkStatusPayment');
    Route::get('reset-stock', [OrderController::class, 'resetAllTicketStock'])->name('resetStock');
    Route::get('send-eticket/{id_order}', function ($id_order) {
        Email::sendEticketPerEmail($id_order);
    })->name('sendEticket');
});

Route::prefix('dj')->name('dj.')->group(function () {
Route::get('/', [DjController::class, 'listDj'])->name('listDj');
    Route::get('{slug}', [DjController::class, 'profileDj'])->name('profileDj');
    Route::get('ajax/list', [DjController::class, 'ajaxListDj'])->name('ajaxListDj');
});

Route::middleware(['custom-auth'])->group(function () {
    Route::get('/my-order', [MyOrderController::class, 'index'])->name('order.myOrder');
    Route::get('/my-order/detail/{order_id}', [MyOrderController::class, 'detail'])->name('order.myOrderDetail');
    Route::get('/settings', [SettingsController::class, 'index'])->name('settings.mySettings');
    Route::post('/settings/personal-info', [SettingsController::class, 'updatePersonalInfo'])->name('settings.update.personal');
    Route::post('/settings/password', [SettingsController::class, 'updatePassword'])->name('settings.update.password');
    Route::post('/settings/email', [SettingsController::class, 'updateEmail'])->name('settings.update.email');
    Route::get('/settings/user-data', [SettingsController::class, 'getUserData'])->name('settings.user.data');
});


Route::prefix('admin')->middleware(['custom-admin'])->name('admin.')->group(function () {
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/booking-list', [DashboardController::class, 'bookingList'])->name('booking-list');
    Route::post('/booking-list', [DashboardController::class, 'bookingList'])->name('booking-listPost');

    // Events routes - ALL specific routes MUST come before dynamic {id} routes
    Route::get('/events', [EventsController::class, 'index'])->name('events');
    Route::post('/events', [EventsController::class, 'store'])->name('events.store');
    Route::get('/events/venues', [EventsController::class, 'getVenues'])->name('events.venues');
    Route::get('/events/venue-data/{venueId}', [EventsController::class, 'getVenueData'])->name('events.venue-data');
    Route::get('/events/djs', [EventsController::class, 'getDjs'])->name('events.djs');
    Route::get('/events/provinces', [EventsController::class, 'getProvinces'])->name('events.provinces');
    Route::get('/events/regencies', [EventsController::class, 'getRegencies'])->name('events.regencies');
    // Test route to verify regencies endpoint
    Route::get('/test-regencies', function() {
        return response()->json(['message' => 'Regencies route is working', 'timestamp' => now()]);
    });
    Route::get('/events/districts', [EventsController::class, 'getDistricts'])->name('events.districts');
    // Dynamic routes with {id} parameter MUST be last
    Route::get('/events/{id}', [EventsController::class, 'show'])->name('events.show');
    Route::put('/events/{id}', [EventsController::class, 'update'])->name('events.update');
    Route::delete('/events/{id}', [EventsController::class, 'destroy'])->name('events.destroy');

    Route::get('/load-more-events', [DashboardController::class, 'loadMoreEvents'])->name('load-more-events');
    // Route::get('/events', [EventController::class, 'index'])->name('events');

    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard/load-more-events', [DashboardController::class, 'loadMoreEvents'])->name('dashboard.loadMoreEvents');

    // Venue routes
    Route::get('/venues', [AdminVenueController::class, 'index'])->name('venues');
    Route::post('/venues', [AdminVenueController::class, 'store'])->name('venues.store');
    Route::put('/venues/{id}', [AdminVenueController::class, 'update'])->name('venues.update');
    Route::delete('/venues/{id}', [AdminVenueController::class, 'destroy'])->name('venues.destroy');
    Route::get('/venues/{id}/open-hours', [AdminVenueController::class, 'getOpenHours'])->name('venues.open-hours');
    Route::get('/venues/{id}/ticket-types', [AdminVenueController::class, 'getTicketTypes'])->name('venues.ticket-types');
    Route::get('/venues/provinces', [AdminVenueController::class, 'getProvinces'])->name('venues.provinces');
    Route::get('/venues/regencies', [AdminVenueController::class, 'getRegencies'])->name('venues.regencies');
    Route::get('/venues/districts', [AdminVenueController::class, 'getDistricts'])->name('venues.districts');

    // QR Scanner routes
    Route::get('/qr-scanner', [App\Http\Controllers\Admin\QRScannerController::class, 'index'])->name('qr-scanner');
    Route::post('/qr-scanner/scan', [App\Http\Controllers\Admin\QRScannerController::class, 'scan'])->name('qr-scanner.scan');

    // DJs management
    Route::get('/djs', [App\Http\Controllers\Admin\DjController::class, 'index'])->name('djs');
    Route::get('/djs/data', [App\Http\Controllers\Admin\DjController::class, 'getDjs'])->name('djs.data');
    Route::post('/djs', [App\Http\Controllers\Admin\DjController::class, 'store'])->name('djs.store');
    Route::get('/djs/{id}', [App\Http\Controllers\Admin\DjController::class, 'show'])->name('djs.show');
    Route::put('/djs/{id}', [App\Http\Controllers\Admin\DjController::class, 'update'])->name('djs.update');
    Route::delete('/djs/{id}', [App\Http\Controllers\Admin\DjController::class, 'destroy'])->name('djs.destroy');
    Route::delete('/djs/gallery/{id}', [App\Http\Controllers\Admin\DjController::class, 'deleteGallery'])->name('djs.gallery.delete');

    // Debug route to check data
    Route::get('/debug-data', function() {
        $user = \Illuminate\Support\Facades\Auth::user();
        $merchantId = $user->id_merchant ?? '1';

        // Get sample orders
        $orders = \Illuminate\Support\Facades\DB::table('tbl_bar_order as o')
            ->join('tbl_bar_venue as v', 'o.id_venue', '=', 'v.id')
            ->where('v.id_merchant', $merchantId)
            ->select('o.id', 'o.full_name', 'o.date', 'v.nama as venue_name')
            ->limit(5)
            ->get();

        // Get sample order details with their IDs (these are the QR codes)
        $orderDetails = \Illuminate\Support\Facades\DB::table('tbl_bar_order_detail')
            ->whereIn('id_order', $orders->pluck('id'))
            ->select('id', 'id_order', 'full_name', 'email', 'phone', 'flag_scanned')
            ->get();

        // Test specific query that might be failing
        $testQuery = \Illuminate\Support\Facades\DB::table('tbl_bar_order_detail')
            ->whereIn('id_order', $orders->pluck('id'))
            ->get()
            ->groupBy('id_order');

        return response()->json([
            'merchant_id' => $merchantId,
            'orders' => $orders,
            'order_details' => $orderDetails,
            'order_ids_looking_for' => $orders->pluck('id')->toArray(),
            'grouped_details' => $testQuery,
            'note' => 'QR codes are the "id" field from order_details table'
        ]);
    });

});

// Route::middleware(['custom-admin'])->group(function () {
//     Route::get('/', [DashboardController::class, 'index'])->name('dashboard');
//     Route::get('/booking-list', [DashboardController::class, 'bookingList'])->name('booking-list');
//     Route::get('/load-more-events', [DashboardController::class, 'loadMoreEvents'])->name('load-more-events');
//     // Add more admin routes here
//     Route::get('/events', [EventController::class, 'index'])->name('events');
// });

Route::prefix('xendit')->name('xendit.')->group(function () {
    Route::post('callback-id', [XenditController::class, 'callbackId'])->name('callbackId');
    Route::post('callback-my', [XenditController::class, 'callbackMy'])->name('callbackMy');
    Route::post('callback-th', [XenditController::class, 'callbackTh'])->name('callbackTh');
    Route::post('callback-ph', [XenditController::class, 'callbackPh'])->name('callbackPh');
    Route::post('callback-vn', [XenditController::class, 'callbackVn'])->name('callbackVn');
    Route::post('simulate-payment', [XenditController::class, 'simulatePayment'])->name('simulatePayment');
});


Route::get('/upload', [EventVenueImportController::class, 'showUploadForm'])->name('upload.form');
Route::post('/import-excel', [EventVenueImportController::class, 'importExcel'])->name('import.excel');
Route::get('/generate-template', [ExcelTemplateGeneratorController::class, 'generateTemplate'])->name('generate.template');
