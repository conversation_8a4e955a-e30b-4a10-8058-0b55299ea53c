@import 'tailwindcss';

@custom-variant dark (&:where(.dark, .dark *));

@source "../views";

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';
@source '../**/*.blade.php';
@source '../**/*.js';

@theme {
    --font-sans: 'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol', 'Noto Color Emoji';
}

@theme {
    --color-kreen-gold: #FFB22C;
    --color-kreen-gold-hover: #D97706;
    --color-kreen-dark: #000000;
    --color-kreen-card: #1a1a1a;
    --color-kreen-gold2: linear-gradient(90deg, #A37A1D 0%, #FFF0C0 28%, #FFEC95 66%, #281A00 100%);
    --font-inter: Inter, sans-serif;
}

/* Untuk Chrome, Safari, Edge, Opera */
input[type=number]::-webkit-outer-spin-button,
input[type=number]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Untuk Firefox */
input[type=number] {
  -moz-appearance: textfield;
}




/* Custom styles */
@layer components {
    .select2-container .select2-selection--single {
        @apply bg-black! text-white! border! border-gray-600! rounded-lg! px-4! py-2! h-[44px]! w-[290px]!;
    }

    .select2-container .select2-selection__arrow {
        @apply hidden!;
    }

    .select2-container .select2-selection__rendered {
        @apply text-white!;
    }

    .select2-container .select2-dropdown {
        @apply bg-black! text-white!;
    }

  .sidebar-active {
      @apply bg-[#333333] text-yellow-400;
  }

  .sidebar-item {
      @apply flex items-center space-x-3 p-3 rounded-lg transition-colors;
  }

  .sidebar-item:hover {
      @apply bg-[#333333] text-yellow-400;
  }

  .card {
      @apply bg-white rounded-xl shadow-sm p-4 sm:p-6 border border-gray-100;
  }

  .btn-primary {
      @apply px-4 sm:px-6 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors font-medium;
  }

  .btn-outline {
      @apply px-4 sm:px-6 py-2 border-2 border-yellow-500 text-yellow-600 rounded-lg hover:bg-yellow-50 transition-colors font-medium;
  }

  .stat-card {
      @apply bg-white rounded-xl shadow-sm p-4 sm:p-6 border border-gray-100;
  }

  .stat-title {
      @apply text-xs sm:text-sm font-medium text-gray-600 mb-2 sm:mb-3;
  }

  .stat-value {
      @apply text-2xl sm:text-3xl font-bold text-gray-900;
  }

  .stat-subtitle {
      @apply text-xs sm:text-sm text-gray-500;
  }

  /* Line clamp utility */
  .line-clamp-2 {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
  }
}


/* Mobile optimizations */
@media (max-width: 640px) {
  .stat-value {
      @apply text-xl;
  }

  .card {
      @apply p-4;
  }
}

/* Prevent body scroll when sidebar is open on mobile */
body.overflow-hidden {
  overflow: hidden;
}

/* Touch targets for mobile */
@media (max-width: 1024px) {
  .sidebar-item {
      @apply p-4;
      min-height: 48px;
  }

  button {
      min-height: 44px;
  }
}

/* @media (max-width: 910px) {
  .hide-below-910 {
      display: none !important;
  }
} */

