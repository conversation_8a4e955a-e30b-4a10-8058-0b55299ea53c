<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class BarEvent extends Model
{
    protected $table = 'tbl_bar_event';
    
    protected $fillable = [
        'id',
        'id_venue',
        'nama',
        'tanggal',
        'slug',
        'gambar',
        'deskripsi',
        'lokasi',
        'id_negara',
        'id_provinsi',
        'id_kota',
        'id_kel<PERSON>han',
        'flag_aktif'
    ];

    protected $casts = [
        'tanggal' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // Scope untuk event yang aktif
    public function scopeActive($query)
    {
        return $query->where('flag_aktif', '1');
    }

    // Scope untuk event terdekat (tanggal >= hari ini)
    public function scopeUpcoming($query)
    {
        return $query->where('tanggal', '>=', Carbon::today());
    }

    // Scope untuk event yang sudah lewat
    public function scopePast($query)
    {
        return $query->where('tanggal', '<', Carbon::today());
    }

    // Relasi dengan venue
    public function venue()
    {
        return $this->belongsTo(BarVenue::class, 'id_venue', 'id');
    }

    // Method untuk mendapatkan event terdekat dengan pagination
    public static function getUpcomingEvents($page = 1, $limit = 5)
    {
        return self::active()
            ->upcoming()
            ->orderBy('tanggal', 'asc')
            ->orderBy('created_at', 'desc')
            ->paginate($limit, ['*'], 'page', $page);
    }

    // Method untuk mendapatkan event terdekat berdasarkan merchant
    public static function getUpcomingEventsByMerchant($merchantId, $page = 1, $limit = 5)
    {
        return self::active()
            ->upcoming()
            ->whereHas('venue', function($query) use ($merchantId) {
                $query->where('id_merchant', $merchantId);
            })
            ->orderBy('tanggal', 'asc')
            ->orderBy('created_at', 'desc')
            ->paginate($limit, ['*'], 'page', $page);
    }

    // Method untuk format tanggal Indonesia
    public function getFormattedDateAttribute()
    {
        if (!$this->tanggal) {
            return '-';
        }

        $days = [
            'Sunday' => 'Minggu',
            'Monday' => 'Senin',
            'Tuesday' => 'Selasa',
            'Wednesday' => 'Rabu',
            'Thursday' => 'Kamis',
            'Friday' => 'Jumat',
            'Saturday' => 'Sabtu'
        ];

        $months = [
            'January' => 'Januari',
            'February' => 'Februari',
            'March' => 'Maret',
            'April' => 'April',
            'May' => 'Mei',
            'June' => 'Juni',
            'July' => 'Juli',
            'August' => 'Agustus',
            'September' => 'September',
            'October' => 'Oktober',
            'November' => 'November',
            'December' => 'Desember'
        ];

        $date = Carbon::parse($this->tanggal);
        $dayName = $days[$date->format('l')];
        $monthName = $months[$date->format('F')];
        
        return $dayName . ', ' . $date->format('d') . ' ' . $monthName . ' ' . $date->format('Y');
    }

    // Method untuk mendapatkan gambar dengan fallback
    public function getImageUrlAttribute()
    {
        if ($this->gambar && !empty($this->gambar)) {
            // Jika gambar adalah URL lengkap
            if (filter_var($this->gambar, FILTER_VALIDATE_URL)) {
                return $this->gambar;
            }
            // Jika gambar adalah path relatif
            return asset('storage/' . $this->gambar);
        }
        
        // Fallback image
        return 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-cTabtQHCWmzSlzd047XQGB0zgGBxm8.png';
    }

    // Method untuk mendapatkan deskripsi singkat
    public function getShortDescriptionAttribute()
    {
        if (!$this->deskripsi) {
            return 'Tidak ada deskripsi';
        }
        
        return strlen($this->deskripsi) > 100 
            ? substr($this->deskripsi, 0, 100) . '...' 
            : $this->deskripsi;
    }
}