@extends('layouts.admin')

@section('title', 'Events Management')

@section('page-title', 'Events')

@section('content')
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 space-y-4 sm:space-y-0">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Events</h1>
            <p class="text-gray-600 mt-1">Manage your events and activities</p>
        </div>
        <button id="add-event-btn" class="px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors font-medium text-sm flex items-center space-x-2">
            <span>Buat Event</span>
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
            </svg>
        </button>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div class="flex flex-col sm:flex-row gap-4 items-center">
            <!-- Search -->
            <div class="flex-1 max-w-md">
                <div class="relative">
                    <input type="text" id="search-filter" placeholder="Search events..." value="{{ $search }}" class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                    <svg class="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
                    </svg>
                </div>
            </div>

            <!-- Venue Filter -->
        <div class="flex items-center space-x-2">
            <select id="venue-filter" class="select2 px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent" style="width: 200px;">
                <option value="all">Semua Venue</option>
                @foreach($venues as $venue)
                    <option value="{{ $venue->id }}" {{ $selectedVenue == $venue->id ? 'selected' : '' }}>{{ $venue->nama }}</option>
                @endforeach
            </select>
        </div>


            <!-- Status Filter -->
            <div class="flex items-center space-x-2">
                <select id="status-filter" class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                    <option value="all">All Status</option>
                    <option value="1" {{ $status == '1' ? 'selected' : '' }}>Active</option>
                    <option value="0" {{ $status == '0' ? 'selected' : '' }}>Inactive</option>
                </select>
            </div>

            <!-- Filter Button -->
            <!-- <button id="filter-btn" class="px-4 py-2 border border-gray-300 rounded-lg text-sm hover:bg-gray-50 transition-colors">
                Filter
                <svg class="w-4 h-4 inline ml-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clip-rule="evenodd"></path>
                </svg>
            </button> -->
        </div>
    </div>

    <!-- Events Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tanggal Event</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Poster Event</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nama Event</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Penjelasan Singkat</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Aksi</th>
                    </tr>
                </thead>
                <tbody id="events-table-body" class="bg-white divide-y divide-gray-200">
                    @foreach($events as $event)
                    <tr class="hover:bg-gray-50">
                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{{ $event['tanggal_event'] }}</td>
                        <td class="px-4 py-4 whitespace-nowrap">
                            <img src="{{ $event['poster_event'] }}" alt="Event Poster" class="w-16 h-16 object-cover rounded-lg">
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{{ $event['nama_event'] }}</td>
                        <td class="px-4 py-4 text-sm text-gray-900 max-w-xs">
                            <div class="truncate">{{ $event['penjelasan_singkat'] }}</div>
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap">
                            @if($event['status_color'] == 'green')
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                    {{ $event['status'] }}
                                </span>
                            @else
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                    {{ $event['status'] }}
                                </span>
                            @endif
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                            <div class="flex space-x-2">
                                <button class="view-detail-btn text-blue-600 hover:text-blue-900" 
                                        data-event='@json($event)' 
                                        title="View Details">
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                        <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </button>
                                <button class="edit-event-btn text-green-600 hover:text-green-900" 
                                        data-event='@json($event)' 
                                        title="Edit Event">
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"></path>
                                    </svg>
                                </button>
                                <!-- Delete button hidden as per user request -->
                                <!-- <button class="delete-event-btn text-red-600 hover:text-red-900"
                                        data-id="{{ $event['id'] }}"
                                        title="Delete Event">
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clip-rule="evenodd"></path>
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                    </svg>
                                </button> -->
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination Info -->
    <div class="flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0 mt-4">
        <div id="pagination-info" class="text-sm text-gray-500">
            Showing {{ count($events) }} results
        </div>
        <button id="load-more-btn" class="px-6 py-2 border-2 border-yellow-500 text-yellow-600 rounded-lg hover:bg-yellow-50 transition-colors font-medium text-sm">
            Lihat Lainnya
        </button>
    </div>
</div>
<!-- Modal Add/Edit Event -->
<div id="event-modal" class="fixed inset-0 bg-black/30 backdrop-blur-sm overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex justify-between items-center mb-4">
                <h3 id="modal-title" class="text-lg font-medium text-gray-900">Add New Event</h3>
                <button id="close-modal" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Modal Content -->
            <form id="event-form" enctype="multipart/form-data">
                @csrf
                <input type="hidden" id="event-id" name="event_id">

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Nama Event *</label>
                        <input type="text" id="nama" name="nama" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Tanggal Event *</label>
                        <input type="date" id="tanggal" name="tanggal" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Status *</label>
                        <select id="flag_aktif" name="flag_aktif" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                            <option value="1">Aktif</option>
                            <option value="0">Tidak Aktif</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Venue</label>
                        <select id="id_venue" name="id_venue" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                            <option value="">Pilih Venue</option>
                            @foreach($venues as $venue)
                                <option value="{{ $venue->id }}">{{ $venue->nama }}</option>
                            @endforeach
                        </select>
                        <p class="text-xs text-gray-500 mt-1">Pilih venue untuk auto-fill data lokasi</p>
                    </div>

                    <!-- Hidden fields for venue auto-complete -->
                    <input type="hidden" id="id_negara" name="id_negara">
                    <input type="hidden" id="venue_lat" name="lat">
                    <input type="hidden" id="venue_lng" name="lng">

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Provinsi</label>
                        <select id="id_provinsi" name="id_provinsi" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                            <option value="">Pilih Provinsi</option>
                            @foreach($provinces as $province)
                                <option value="{{ $province->id }}">{{ $province->name }}</option>
                            @endforeach
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Kota/Kabupaten</label>
                        <select id="id_kota" name="id_kota" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                            <option value="">Pilih Provinsi Dulu</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Kecamatan</label>
                        <select id="id_kelurahan" name="id_kelurahan" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                            <option value="">Pilih Kota Dulu</option>
                        </select>
                    </div>

                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Alamat Lengkap *</label>
                        <input type="text" id="lokasi" name="lokasi" required placeholder="Masukkan alamat lengkap..." class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                    </div>

                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Poster Event</label>
                        <input type="file" id="gambar" name="gambar" accept="image/*" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                        <p class="text-xs text-gray-500 mt-1">Format: JPG, PNG, GIF. Max size: 2MB</p>
                    </div>

                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Deskripsi</label>
                        <textarea id="deskripsi" name="deskripsi" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"></textarea>
                    </div>

                    <!-- DJ Selection -->
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">DJ Selection</label>
                        <div class="border border-gray-300 rounded-lg p-3 max-h-48 overflow-y-auto">
                            <div id="dj-loading" class="text-center py-4 text-gray-500">
                                <div class="animate-spin inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full mr-2"></div>
                                Loading DJs...
                            </div>
                            <div id="dj-list" class="space-y-2 hidden">
                                <!-- DJ checkboxes will be loaded here -->
                            </div>
                            <div id="dj-error" class="text-center py-4 text-red-500 hidden">
                                Failed to load DJs. Please try again.
                            </div>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Select DJs to perform at this event</p>
                    </div>
                </div>

                <!-- Modal Footer -->
                <div class="flex justify-end mt-6 space-x-3">
                    <button type="button" id="cancel-btn" class="px-4 py-2 border-2 border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium text-sm">
                        Cancel
                    </button>
                    <button type="submit" id="submit-btn" class="px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors font-medium text-sm">
                        Save Event
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Detail Event -->
<div id="detail-modal" class="fixed inset-0 bg-black/30 backdrop-blur-sm overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Event Details</h3>
                <button id="close-detail-modal" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Modal Content -->
            <div class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Nama Event</label>
                        <p id="detail-nama" class="text-sm text-gray-900 font-semibold">-</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Tanggal Event</label>
                        <p id="detail-tanggal" class="text-sm text-gray-900">-</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Venue</label>
                        <p id="detail-venue" class="text-sm text-gray-900">-</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Lokasi</label>
                        <p id="detail-lokasi" class="text-sm text-gray-900">-</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <span id="detail-status" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">-</span>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Poster Event</label>
                    <img id="detail-poster" src="" alt="Event Poster" class="w-32 h-32 object-cover rounded-lg">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Deskripsi</label>
                    <p id="detail-deskripsi" class="text-sm text-gray-900">-</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">DJs</label>
                    <div id="detail-djs" class="space-y-2">
                        <!-- DJ list will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom Alert Modal -->
<div id="custom-alert" class="fixed inset-0 bg-black/30 backdrop-blur-sm overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <!-- Alert Icon -->
            <div id="alert-icon" class="mx-auto flex items-center justify-center h-12 w-12 rounded-full mb-4">
                <!-- Success Icon -->
                <svg id="success-icon" class="h-6 w-6 text-green-600 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <!-- Error Icon -->
                <svg id="error-icon" class="h-6 w-6 text-red-600 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                <!-- Warning Icon -->
                <svg id="warning-icon" class="h-6 w-6 text-yellow-600 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <!-- Info Icon -->
                <svg id="info-icon" class="h-6 w-6 text-blue-600 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>

            <!-- Alert Title -->
            <h3 id="alert-title" class="text-lg leading-6 font-medium text-gray-900 mb-2">Alert</h3>

            <!-- Alert Message -->
            <div id="alert-message" class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500">Alert message goes here</p>
            </div>

            <!-- Alert Buttons -->
            <div id="alert-buttons" class="items-center px-4 py-3">
                <button id="alert-ok-btn" class="px-4 py-2 bg-blue-500 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-300">
                    OK
                </button>
            </div>

            <!-- Confirm Buttons (for confirm dialogs) -->
            <div id="confirm-buttons" class="items-center px-4 py-3 hidden">
                <div class="flex space-x-3">
                    <button id="confirm-cancel-btn" class="px-4 py-2 bg-gray-300 text-gray-700 text-base font-medium rounded-md flex-1 shadow-sm hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300">
                        Cancel
                    </button>
                    <button id="confirm-ok-btn" class="px-4 py-2 bg-red-500 text-white text-base font-medium rounded-md flex-1 shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300">
                        Delete
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Custom Alert Functions
class CustomAlert {
    constructor() {
        this.modal = document.getElementById('custom-alert');
        this.title = document.getElementById('alert-title');
        this.message = document.getElementById('alert-message');
        this.iconContainer = document.getElementById('alert-icon');
        this.okBtn = document.getElementById('alert-ok-btn');
        this.alertButtons = document.getElementById('alert-buttons');
        this.confirmButtons = document.getElementById('confirm-buttons');
        this.confirmOkBtn = document.getElementById('confirm-ok-btn');
        this.confirmCancelBtn = document.getElementById('confirm-cancel-btn');

        // Icons
        this.successIcon = document.getElementById('success-icon');
        this.errorIcon = document.getElementById('error-icon');
        this.warningIcon = document.getElementById('warning-icon');
        this.infoIcon = document.getElementById('info-icon');

        this.setupEventListeners();
    }

    setupEventListeners() {
        this.okBtn.addEventListener('click', () => this.hide());
        this.confirmCancelBtn.addEventListener('click', () => this.hide());

        // Close on backdrop click
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) this.hide();
        });

        // Close on Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && !this.modal.classList.contains('hidden')) {
                this.hide();
            }
        });
    }

    show(type, title, message, options = {}) {
        this.setIcon(type);
        this.title.textContent = title;
        this.message.innerHTML = `<p class="text-sm text-gray-500">${message}</p>`;

        // Show appropriate buttons
        this.alertButtons.classList.remove('hidden');
        this.confirmButtons.classList.add('hidden');

        // Set button color based on type
        this.setButtonColor(type);

        this.modal.classList.remove('hidden');

        // Auto focus on OK button
        setTimeout(() => this.okBtn.focus(), 100);

        return new Promise((resolve) => {
            this.okBtn.onclick = () => {
                this.hide();
                resolve(true);
            };
        });
    }

    confirm(title, message, options = {}) {
        this.setIcon('warning');
        this.title.textContent = title;
        this.message.innerHTML = `<p class="text-sm text-gray-500">${message}</p>`;

        // Show confirm buttons
        this.alertButtons.classList.add('hidden');
        this.confirmButtons.classList.remove('hidden');

        // Set button text
        this.confirmOkBtn.textContent = options.confirmText || 'Delete';
        this.confirmCancelBtn.textContent = options.cancelText || 'Cancel';

        this.modal.classList.remove('hidden');

        // Auto focus on cancel button for safety
        setTimeout(() => this.confirmCancelBtn.focus(), 100);

        return new Promise((resolve) => {
            this.confirmOkBtn.onclick = () => {
                this.hide();
                resolve(true);
            };
            this.confirmCancelBtn.onclick = () => {
                this.hide();
                resolve(false);
            };
        });
    }

    setIcon(type) {
        // Hide all icons
        this.successIcon.classList.add('hidden');
        this.errorIcon.classList.add('hidden');
        this.warningIcon.classList.add('hidden');
        this.infoIcon.classList.add('hidden');

        // Remove all background colors
        this.iconContainer.className = 'mx-auto flex items-center justify-center h-12 w-12 rounded-full mb-4';

        switch(type) {
            case 'success':
                this.successIcon.classList.remove('hidden');
                this.iconContainer.classList.add('bg-green-100');
                break;
            case 'error':
                this.errorIcon.classList.remove('hidden');
                this.iconContainer.classList.add('bg-red-100');
                break;
            case 'warning':
                this.warningIcon.classList.remove('hidden');
                this.iconContainer.classList.add('bg-yellow-100');
                break;
            case 'info':
            default:
                this.infoIcon.classList.remove('hidden');
                this.iconContainer.classList.add('bg-blue-100');
                break;
        }
    }

    setButtonColor(type) {
        // Remove all color classes
        this.okBtn.className = 'px-4 py-2 text-white text-base font-medium rounded-md w-full shadow-sm focus:outline-none focus:ring-2';

        switch(type) {
            case 'success':
                this.okBtn.classList.add('bg-green-500', 'hover:bg-green-700', 'focus:ring-green-300');
                break;
            case 'error':
                this.okBtn.classList.add('bg-red-500', 'hover:bg-red-700', 'focus:ring-red-300');
                break;
            case 'warning':
                this.okBtn.classList.add('bg-yellow-500', 'hover:bg-yellow-700', 'focus:ring-yellow-300');
                break;
            case 'info':
            default:
                this.okBtn.classList.add('bg-blue-500', 'hover:bg-blue-700', 'focus:ring-blue-300');
                break;
        }
    }

    hide() {
        this.modal.classList.add('hidden');
    }

    // Convenience methods
    success(title, message) {
        return this.show('success', title, message);
    }

    error(title, message) {
        return this.show('error', title, message);
    }

    warning(title, message) {
        return this.show('warning', title, message);
    }

    info(title, message) {
        return this.show('info', title, message);
    }
}

// Initialize custom alert
const customAlert = new CustomAlert();
document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const searchFilter = document.getElementById('search-filter');
    const venueFilter = document.getElementById('venue-filter');
    const statusFilter = document.getElementById('status-filter');
    const filterBtn = document.getElementById('filter-btn');
    const tableBody = document.getElementById('events-table-body');
    const paginationInfo = document.getElementById('pagination-info');

    // Modal elements
    const eventModal = document.getElementById('event-modal');
    const detailModal = document.getElementById('detail-modal');
    const addEventBtn = document.getElementById('add-event-btn');
    const closeModal = document.getElementById('close-modal');
    const closeDetailModal = document.getElementById('close-detail-modal');
    const cancelBtn = document.getElementById('cancel-btn');
    const eventForm = document.getElementById('event-form');

    // Event listeners for filters
    // filterBtn.addEventListener('click', loadEvents);

    // Search with debounce
    let searchTimeout;
    searchFilter.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(loadEvents, 500);
    });

    venueFilter.addEventListener('change', loadEvents);
    statusFilter.addEventListener('change', loadEvents);

    // Modal event listeners
    addEventBtn.addEventListener('click', () => showEventModal());
    closeModal.addEventListener('click', hideEventModal);
    closeDetailModal.addEventListener('click', hideDetailModal);
    cancelBtn.addEventListener('click', hideEventModal);

    // Close modal when clicking outside
    eventModal.addEventListener('click', function(e) {
        if (e.target === eventModal) hideEventModal();
    });

    detailModal.addEventListener('click', function(e) {
        if (e.target === detailModal) hideDetailModal();
    });

    // Venue auto-complete event listener
    document.addEventListener('change', function(e) {
        if (e.target && e.target.id === 'id_venue') {
            handleVenueChange();
        }
    });

    // Event delegation for dynamic buttons
    document.addEventListener('click', function(e) {
        if (e.target.closest('.view-detail-btn')) {
            const btn = e.target.closest('.view-detail-btn');
            try {
                const eventData = btn.dataset.event.replace(/&quot;/g, '"');
                const event = JSON.parse(eventData);
                showDetailModal(event);
            } catch (error) {
                console.error('Error parsing event data:', error);
                customAlert.error('Error', 'Failed to load event details. Please try again.');
            }
        }

        if (e.target.closest('.edit-event-btn')) {
            const btn = e.target.closest('.edit-event-btn');
            try {
                const eventData = btn.dataset.event.replace(/&quot;/g, '"');
                const event = JSON.parse(eventData);
                showEventModal(event);
            } catch (error) {
                console.error('Error parsing event data:', error);
                customAlert.error('Error', 'Failed to load event for editing. Please try again.');
            }
        }

        // Delete functionality disabled as per user request
        // if (e.target.closest('.delete-event-btn')) {
        //     const btn = e.target.closest('.delete-event-btn');
        //     const eventId = btn.dataset.id;
        //     deleteEvent(eventId);
        // }
    });

    // Form submission
    eventForm.addEventListener('submit', function(e) {
        e.preventDefault();
        saveEvent();
    });

    // Location dropdown handlers
    document.getElementById('id_provinsi').addEventListener('change', function() {
        const provinceId = this.value;
        const kotaSelect = document.getElementById('id_kota');
        const kelurahanSelect = document.getElementById('id_kelurahan');

        // Reset dependent dropdowns
        kotaSelect.disabled = true;
        kotaSelect.innerHTML = `
            <option value="">
                <svg class="animate-spin h-4 w-4 inline mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Loading...
            </option>
        `;
        kelurahanSelect.innerHTML = '<option value="">Pilih Kota Dulu</option>';

        if (provinceId) {
            fetch(`/admin/events/regencies?province_id=${provinceId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        kotaSelect.innerHTML = '<option value="">Pilih Kota/Kabupaten</option>';
                        data.regencies.forEach(regency => {
                            kotaSelect.innerHTML += `<option value="${regency.id}">${regency.name}</option>`;
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading regencies:', error);
                    kotaSelect.innerHTML = '<option value="">Error loading data</option>';
                })
                .finally(() => {
                    kotaSelect.disabled = false;
                });
        } else {
            kotaSelect.innerHTML = '<option value="">Pilih Provinsi Dulu</option>';
            kotaSelect.disabled = false;
        }
    });

    document.getElementById('id_kota').addEventListener('change', function() {
        const regencyId = this.value;
        const kelurahanSelect = document.getElementById('id_kelurahan');

        // Reset dependent dropdown
        kelurahanSelect.disabled = true;
        kelurahanSelect.innerHTML = `
            <option value="">
                <svg class="animate-spin h-4 w-4 inline mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Loading...
            </option>
        `;

        if (regencyId) {
            fetch(`/admin/events/districts?regency_id=${regencyId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        kelurahanSelect.innerHTML = '<option value="">Pilih Kecamatan</option>';
                        data.districts.forEach(district => {
                            kelurahanSelect.innerHTML += `<option value="${district.id}">${district.name}</option>`;
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading districts:', error);
                    kelurahanSelect.innerHTML = '<option value="">Error loading data</option>';
                })
                .finally(() => {
                    kelurahanSelect.disabled = false;
                });
        } else {
            kelurahanSelect.innerHTML = '<option value="">Pilih Kota Dulu</option>';
            kelurahanSelect.disabled = false;
        }
    });

    function loadEvents() {
        // Show loading
        tableBody.innerHTML = `
            <tr>
                <td colspan="6" class="px-4 py-8 text-center text-gray-500">
                    <div class="flex items-center justify-center space-x-2">
                        <svg class="animate-spin h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Loading events...</span>
                    </div>
                </td>
            </tr>
        `;

        // Prepare data
        const params = new URLSearchParams({
            search: searchFilter.value,
            venue: venueFilter.value,
            status: statusFilter.value
        });

        // Make AJAX request
        fetch(`{{ route("admin.events") }}?${params.toString()}`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.events && data.events.length > 0) {
                    let html = '';
                    data.events.forEach(event => {
                        html += createEventRow(event);
                    });
                    tableBody.innerHTML = html;
                    paginationInfo.innerHTML = `Showing ${data.total_count} results`;
                } else {
                    tableBody.innerHTML = `
                        <tr>
                            <td colspan="6" class="px-4 py-8 text-center text-gray-500">
                                <div class="flex flex-col items-center space-y-2">
                                    <svg class="w-12 h-12 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    <span>No events found</span>
                                </div>
                            </td>
                        </tr>
                    `;
                    paginationInfo.innerHTML = 'No results found';
                }
            } else {
                throw new Error(data.error || 'Unknown error occurred');
            }
        })
        .catch(error => {
            console.error('Error loading events:', error);
            tableBody.innerHTML = `
                <tr>
                    <td colspan="6" class="px-4 py-8 text-center text-red-500">
                        <span>Error: ${error.message}</span>
                    </td>
                </tr>
            `;
        });
    }

    function createEventRow(event) {
        const statusClass = event.status_color === 'green' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
        const eventData = JSON.stringify(event).replace(/"/g, '&quot;');

        return `
            <tr class="hover:bg-gray-50">
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">${event.tanggal_event}</td>
                <td class="px-4 py-4 whitespace-nowrap">
                    <img src="${event.poster_event}" alt="Event Poster" class="w-16 h-16 object-cover rounded-lg" onerror="this.src='/images/default-event.jpg'">
                </td>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">${event.nama_event}</td>
                <td class="px-4 py-4 text-sm text-gray-900 max-w-xs">
                    <div class="truncate">${event.penjelasan_singkat}</div>
                </td>
                <td class="px-4 py-4 whitespace-nowrap">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusClass}">
                        ${event.status}
                    </span>
                </td>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div class="flex space-x-2">
                        <button class="view-detail-btn text-blue-600 hover:text-blue-900"
                                data-event="${eventData}"
                                title="View Details">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                        <button class="edit-event-btn text-green-600 hover:text-green-900"
                                data-event="${eventData}"
                                title="Edit Event">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"></path>
                            </svg>
                        </button>
                        <!-- Delete button hidden as per user request -->
                        <!-- <button class="delete-event-btn text-red-600 hover:text-red-900"
                                data-id="${event.id}"
                                title="Delete Event">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clip-rule="evenodd"></path>
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                            </svg>
                        </button> -->
                    </div>
                </td>
            </tr>
        `;
    }

    function showEventModal(event = null) {
        const modalTitle = document.getElementById('modal-title');
        const submitBtn = document.getElementById('submit-btn');

        if (event) {
            modalTitle.textContent = 'Edit Event';
            submitBtn.textContent = 'Update Event';

            // Fill form with event data
            document.getElementById('event-id').value = event.id;
            document.getElementById('nama').value = event.nama_event;
            document.getElementById('tanggal').value = event.tanggal_raw;
            document.getElementById('lokasi').value = event.lokasi;
            document.getElementById('deskripsi').value = event.deskripsi_full;
            document.getElementById('flag_aktif').value = event.flag_aktif;
            document.getElementById('id_venue').value = event.id_venue || '';

            // Set location dropdowns if data exists
            if (event.id_provinsi) {
                document.getElementById('id_provinsi').value = event.id_provinsi;
                // Load regencies for this province
                loadRegencies(event.id_provinsi, event.id_kota);
            }
            if (event.id_kota && event.id_provinsi) {
                // Load districts for this regency
                loadDistricts(event.id_kota, event.id_kelurahan);
            }

            // Load DJs and then select the assigned ones
            loadDJs().then(() => {
                loadAssignedDJs(event.id);
            });
        } else {
            modalTitle.textContent = 'Add New Event';
            submitBtn.textContent = 'Save Event';
            eventForm.reset();
            document.getElementById('event-id').value = '';
            document.getElementById('flag_aktif').value = '1'; // Default to active

            // Reset location dropdowns
            document.getElementById('id_kota').innerHTML = '<option value="">Pilih Provinsi Dulu</option>';
            document.getElementById('id_kelurahan').innerHTML = '<option value="">Pilih Kota Dulu</option>';

            // Load DJs for new event (no assigned DJs to load)
            loadDJs();
        }

        eventModal.classList.remove('hidden');
    }

    // Load DJs for selection
    function loadDJs() {
        const djLoading = document.getElementById('dj-loading');
        const djList = document.getElementById('dj-list');
        const djError = document.getElementById('dj-error');

        djLoading.classList.remove('hidden');
        djList.classList.add('hidden');
        djError.classList.add('hidden');

        return fetch('/admin/events/djs')
            .then(response => {
                console.log('DJ API Response Status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('DJ API Response Data:', data);
                if (data.success) {
                    djLoading.classList.add('hidden');
                    djList.classList.remove('hidden');

                    if (data.djs && data.djs.length > 0) {
                        let djHtml = '';
                        data.djs.forEach(dj => {
                            const djImage = dj.gambar
                                ? `<img src="${dj.gambar}" alt="${dj.nama}" class="w-8 h-8 rounded-full object-cover">`
                                : `<div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                    </svg>
                                   </div>`;

                            djHtml += `
                                <label class="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded cursor-pointer">
                                    <input type="checkbox" name="selected_djs[]" value="${dj.id}" class="rounded border-gray-300 text-yellow-600 focus:ring-yellow-500">
                                    ${djImage}
                                    <div class="flex-1">
                                        <div class="text-sm font-medium text-gray-900">${dj.nama}</div>
                                        <div class="text-xs text-gray-500">${dj.origin || ''} ${dj.gender ? `• ${dj.gender}` : ''}</div>
                                    </div>
                                </label>
                            `;
                        });
                        djList.innerHTML = djHtml;
                    } else {
                        // No DJs available
                        djList.innerHTML = `
                            <div class="text-center py-4 text-gray-500">
                                <svg class="w-8 h-8 mx-auto mb-2 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                </svg>
                                <p class="text-sm">No DJs available</p>
                                <p class="text-xs text-gray-400">Please add DJs to the system first</p>
                            </div>
                        `;
                    }
                    return Promise.resolve();
                } else {
                    djLoading.classList.add('hidden');
                    djError.classList.remove('hidden');
                    djError.innerHTML = `
                        <div class="text-center py-4 text-red-500">
                            <p class="text-sm">Failed to load DJs</p>
                            <p class="text-xs">${data.message || 'Please try again'}</p>
                        </div>
                    `;
                    return Promise.reject(data.message || 'Failed to load DJs');
                }
            })
            .catch(error => {
                console.error('Error loading DJs:', error);
                djLoading.classList.add('hidden');
                djError.classList.remove('hidden');
                return Promise.reject(error);
            });
    }

    // Load assigned DJs for edit mode
    function loadAssignedDJs(eventId) {
        if (!eventId) {
            console.log('loadAssignedDJs: No event ID provided, skipping');
            return;
        }

        console.log('loadAssignedDJs called with event ID:', eventId);
        fetch(`/admin/events/${eventId}`)
            .then(response => {
                console.log('Event show API response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Event show API response data:', data);
                if (data.success && data.assigned_djs) {
                    // Check the checkboxes for assigned DJs
                    data.assigned_djs.forEach(dj => {
                        const checkbox = document.querySelector(`input[name="selected_djs[]"][value="${dj.id}"]`);
                        if (checkbox) {
                            checkbox.checked = true;
                        }
                    });
                }
            })
            .catch(error => {
                console.error('Error loading assigned DJs:', error);
            });
    }

    // Auto-complete venue data
    function handleVenueChange() {
        const venueSelect = document.getElementById('id_venue');
        const venueId = venueSelect.value;

        console.log('handleVenueChange called', { venueId: venueId });

        if (venueId) {
            console.log('Fetching venue data for venue ID:', venueId);
            fetch(`/admin/events/venue-data/${venueId}`)
                .then(response => {
                    console.log('Venue API response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Venue data response:', data);

                    if (data.success) {
                        const venue = data.venue;

                        // Fill hidden fields
                        document.getElementById('id_negara').value = venue.id_negara || '';
                        document.getElementById('venue_lat').value = venue.lat || '';
                        document.getElementById('venue_lng').value = venue.lng || '';

                        // Auto-fill location dropdowns if user hasn't selected anything
                        const provinsiSelect = document.getElementById('id_provinsi');
                        const kotaSelect = document.getElementById('id_kota');
                        const kelurahanSelect = document.getElementById('id_kelurahan');
                        const lokasiInput = document.getElementById('lokasi');

                        // Auto-fill provinsi only if venue has the data
                        if (!provinsiSelect.value && venue.id_propinsi) {
                            provinsiSelect.value = venue.id_propinsi;

                            // Load regencies and then set kota
                            loadRegencies(venue.id_propinsi).then(() => {
                                if (venue.id_kota) {
                                    kotaSelect.value = venue.id_kota;

                                    // Load districts and then set kelurahan
                                    loadDistricts(venue.id_kota).then(() => {
                                        if (venue.id_kelurahan) {
                                            kelurahanSelect.value = venue.id_kelurahan;
                                        }
                                    }).catch(error => {
                                        console.log('Districts loading failed:', error);
                                    });
                                }
                            }).catch(error => {
                                console.log('Regencies loading failed:', error);
                            });
                        }

                        // Auto-fill alamat only if venue has the data
                        if (!lokasiInput.value && venue.alamat) {
                            lokasiInput.value = venue.alamat;
                        }

                        // Show message if venue data is incomplete
                        if (data.message) {
                            console.log('Venue message:', data.message);
                        }
                    } else {
                        console.log('Venue data not successful:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error loading venue data:', error);
                });
        } else {
            // Clear auto-filled data when no venue selected
            document.getElementById('id_negara').value = '';
            document.getElementById('venue_lat').value = '';
            document.getElementById('venue_lng').value = '';
        }
    }

    function loadRegencies(provinceId, selectedRegencyId = null) {
        const kotaSelect = document.getElementById('id_kota');
        kotaSelect.disabled = true;
        kotaSelect.innerHTML = `
            <option value="">
                <svg class="animate-spin h-4 w-4 inline mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Loading...
            </option>
        `;

        console.log('loadRegencies called with province_id:', provinceId);
        return fetch(`/admin/events/regencies?province_id=${provinceId}`)
            .then(response => {
                console.log('Regencies API response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Regencies API response data:', data);
                if (data.success) {
                    kotaSelect.innerHTML = '<option value="">Pilih Kota/Kabupaten</option>';
                    data.regencies.forEach(regency => {
                        const selected = selectedRegencyId && regency.id == selectedRegencyId ? 'selected' : '';
                        kotaSelect.innerHTML += `<option value="${regency.id}" ${selected}>${regency.name}</option>`;
                    });
                    return Promise.resolve();
                } else {
                    return Promise.reject('Failed to load regencies');
                }
            })
            .catch(error => {
                console.error('Error loading regencies:', error);
                kotaSelect.innerHTML = '<option value="">Error loading data</option>';
                return Promise.reject(error);
            })
            .finally(() => {
                kotaSelect.disabled = false;
            });
    }

    function loadDistricts(regencyId, selectedDistrictId = null) {
        const kelurahanSelect = document.getElementById('id_kelurahan');
        kelurahanSelect.disabled = true;
        kelurahanSelect.innerHTML = `
            <option value="">
                <svg class="animate-spin h-4 w-4 inline mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Loading...
            </option>
        `;

        console.log('loadDistricts called with regency_id:', regencyId);
        return fetch(`/admin/events/districts?regency_id=${regencyId}`)
            .then(response => {
                console.log('Districts API response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Districts API response data:', data);
                if (data.success) {
                    kelurahanSelect.innerHTML = '<option value="">Pilih Kecamatan</option>';
                    data.districts.forEach(district => {
                        const selected = selectedDistrictId && district.id == selectedDistrictId ? 'selected' : '';
                        kelurahanSelect.innerHTML += `<option value="${district.id}" ${selected}>${district.name}</option>`;
                    });
                    return Promise.resolve();
                } else {
                    return Promise.reject('Failed to load districts');
                }
            })
            .catch(error => {
                console.error('Error loading districts:', error);
                kelurahanSelect.innerHTML = '<option value="">Error loading data</option>';
                return Promise.reject(error);
            })
            .finally(() => {
                kelurahanSelect.disabled = false;
            });
    }

    function hideEventModal() {
        eventModal.classList.add('hidden');
        eventForm.reset();
    }

    function showDetailModal(event) {
        document.getElementById('detail-nama').textContent = event.nama_event;
        document.getElementById('detail-tanggal').textContent = event.tanggal_event;
        document.getElementById('detail-venue').textContent = event.venue_nama || 'No venue selected';
        document.getElementById('detail-lokasi').textContent = event.lokasi;
        document.getElementById('detail-deskripsi').textContent = event.deskripsi_full || 'No description available';

        // Load and display DJs
        loadEventDJs(event.id);
        document.getElementById('detail-poster').src = event.poster_event;

        const statusSpan = document.getElementById('detail-status');
        statusSpan.textContent = event.status;
        statusSpan.className = `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${event.status_color === 'green' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`;

        detailModal.classList.remove('hidden');
    }

    // Load DJs for event detail modal
    function loadEventDJs(eventId) {
        const djContainer = document.getElementById('detail-djs');
        djContainer.innerHTML = '<div class="text-center py-2 text-gray-500">Loading DJs...</div>';

        fetch(`/admin/events/${eventId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.assigned_djs && data.assigned_djs.length > 0) {
                    let djHtml = '';
                    data.assigned_djs.forEach(dj => {
                        const djImage = dj.gambar
                            ? `<img src="${dj.gambar}" alt="${dj.nama}" class="w-10 h-10 rounded-full object-cover">`
                            : `<div class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                </svg>
                               </div>`;

                        djHtml += `
                            <div class="flex items-center space-x-3 p-2 bg-gray-50 rounded-lg">
                                ${djImage}
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-gray-900">${dj.nama}</div>
                                    <div class="text-xs text-gray-500">${dj.origin || ''} ${dj.gender ? `• ${dj.gender}` : ''}</div>
                                </div>
                            </div>
                        `;
                    });
                    djContainer.innerHTML = djHtml;
                } else {
                    djContainer.innerHTML = '<div class="text-center py-4 text-gray-500">No DJs assigned to this event</div>';
                }
            })
            .catch(error => {
                console.error('Error loading event DJs:', error);
                djContainer.innerHTML = '<div class="text-center py-4 text-red-500">Failed to load DJs</div>';
            });
    }

    function hideDetailModal() {
        detailModal.classList.add('hidden');
    }

    function saveEvent() {
        const formData = new FormData(eventForm);
        const eventId = document.getElementById('event-id').value;
        const submitBtn = document.getElementById('submit-btn');
        const originalText = submitBtn.textContent;

        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = `
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            ${eventId ? 'Updating...' : 'Saving...'}
        `;

        let url, method;
        if (eventId) {
            // Update existing event
            url = `/admin/events/${eventId}`;
            method = 'POST';
            formData.append('_method', 'PUT');
            formData.append('_token', '{{ csrf_token() }}');
        } else {
            // Create new event
            url = '{{ route("admin.events.store") }}';
            method = 'POST';
        }

        fetch(url, {
            method: method,
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                hideEventModal();
                loadEvents();
                customAlert.success('Success', data.message);
            } else {
                customAlert.error('Error', data.message || 'An error occurred while saving the event.');
            }
        })
        .catch(error => {
            console.error('Error saving event:', error);
            customAlert.error('Error', 'Failed to save event. Please check your connection and try again.');
        })
        .finally(() => {
            // Reset button state
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        });
    }

    async function deleteEvent(eventId) {
        const confirmed = await customAlert.confirm(
            'Delete Event',
            'Are you sure you want to delete this event? This action cannot be undone.',
            { confirmText: 'Delete', cancelText: 'Cancel' }
        );

        if (confirmed) {
            // Find the delete button and show loading
            const deleteBtn = document.querySelector(`[data-id="${eventId}"]`);
            const originalHTML = deleteBtn.innerHTML;

            deleteBtn.disabled = true;
            deleteBtn.innerHTML = `
                <svg class="animate-spin h-4 w-4 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            `;

            fetch(`/admin/events/${eventId}`, {
                method: 'DELETE',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadEvents();
                    customAlert.success('Deleted', data.message);
                } else {
                    customAlert.error('Error', data.message || 'Failed to delete event.');
                }
            })
            .catch(error => {
                console.error('Error deleting event:', error);
                customAlert.error('Error', 'Failed to delete event. Please check your connection and try again.');
            })
            .finally(() => {
                // Reset button state if still exists
                if (deleteBtn) {
                    deleteBtn.disabled = false;
                    deleteBtn.innerHTML = originalHTML;
                }
            });
        }
    }
});
</script>
@endpush

@endsection
