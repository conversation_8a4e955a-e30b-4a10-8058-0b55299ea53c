@extends('vip.app')

@section('title', 'Ticket Confirmation - KREEN VIP')

@push('styles')
<style>
.sticky-card {
    position: sticky;
    top: 2rem;
    z-index: 10;
}

.booking-container {
    position: relative;
    overflow: visible;
}

.booking-grid {
    position: relative;
    overflow: visible;
}

/* Full width section */
.full-width-section {
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    padding-left: calc(50vw - 50%);
    padding-right: calc(50vw - 50%);
}

/* Header with background image */
.header-bg {
    background-image: url('https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');
    background-size: cover;
    background-position: center;
    position: relative;
}

.header-bg::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(0,0,0,0.7), rgba(0,0,0,0.5));
    z-index: 1;
}

.header-content {
    position: relative;
    z-index: 2;
}

/* Mobile sticky adjustments */
@media (max-width: 1024px) {
    .sticky-card {
        position: relative;
        top: auto;
    }
}

/* Progress indicator responsive design */
@media (max-width: 768px) {
    .booking-progress {
        display: none !important;
    }
    
    .booking-progress-mobile {
        display: flex !important;
        justify-content: center;
        padding: 0.5rem 0;
        border-top: 1px solid #374151;
        margin-top: 1rem;
    }
}

@media (min-width: 769px) {
    .booking-progress-mobile {
        display: none !important;
    }
}

/* Success animation */
@keyframes checkmark {
    0% { transform: scale(0); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.success-checkmark {
    animation: checkmark 0.6s ease-in-out;
}

/* Ticket dotted line separator */
.ticket-separator {
    border-top: 2px dashed #ffffff;
    margin: 1.5rem 0;
    opacity: 0.6;
}

/* Better QR code styling */
.qr-code {
    background: white;
    padding: 0.75rem;
    border-radius: 0.375rem;
    display: inline-block;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* QR code pattern */
.qr-pattern {
    width: 8rem;
    height: 8rem;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Order card styling */
.order-card {
    /* background: linear-gradient(135deg, rgba(60, 60, 60, 0.9), rgba(80, 80, 80, 0.9)); */
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.order-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.order-image {
    width: 100px;
    height: 80px;
    border-radius: 0.75rem;
    object-fit: cover;
    flex-shrink: 0;
}

.order-details {
    flex: 1;
    color: white;
}

.order-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: white;
}

.order-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.25rem;
    color: #D1D5DB;
    font-size: 0.875rem;
}

.order-info svg {
    width: 16px;
    height: 16px;
    color: #9CA3AF;
}

.view-button {
    background: #D4AF37;
    color: black;
    padding: 0.5rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 0.875rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.view-button:hover {
    background: #B8941F;
    transform: scale(1.05);
}

@media (max-width: 768px) {
    .order-card {
        flex-direction: column;
        text-align: center;
    }
    
    .order-image {
        width: 100%;
        height: 120px;
    }
}

</style>
@endpush

@section('content')

<!-- Combined Success Message and Ticket Details Section -->
<section class="full-width-section py-16 bg-gradient-to-b from-black to-gray-900">
    <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">

            <!-- Page Title -->
            <div class="text-start mb-3">
                <h1 class="text-white text-4xl font-bold mb-4">My Order</h1>
            </div>

             <!-- Back Button -->
             <div class="mb-8">
                <button onclick="goBack()" class="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"/>
                    </svg>
                    <span>Back</span>
                </button>
            </div>


            <div class="order-card bg-white/20">
                    <img src="{{ $order->venue_banner ?? 'https://via.placeholder.com/300x200' }}" alt="{{ $order->venue_name }}" 
                         alt="{{ $order->venue_name }}" 
                         class="order-image">
                    
                    <div class="order-details">
                    @if ($order->status != 404)
                        <span class="inline-block text-xs text-white font-semibold px-2 py-1 rounded mb-2 {{ $order->badge_color }}">
                            {{ $order->badge_label }}
                        </span>
                    @endif
                        <h3 class="order-title">{{ $order->venue_name }}</h3>
                        
                        <div class="order-info">
                            <svg fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                            </svg>
                            <span>{{ \Carbon\Carbon::parse($order->date)->format('l, d F Y') }}</span>
                        </div>
                        
                        <div class="order-info">
                            <svg fill="currentColor" viewBox="0 0 20 20">
                                <path d="M2 6a2 2 0 012-2h12a2 2 0 012 2v2a2 2 0 100 4v2a2 2 0 01-2 2H4a2 2 0 01-2-2v-2a2 2 0 100-4V6z"/>
                            </svg>
                            <span>{{ $totalTickets }} {{ $totalTickets > 1 ? 'tickets' : 'ticket' }}</span>
                        </div>
                    </div>
                    
                    <!-- <button class="view-button" onclick="viewOrder('order1')">View</button> -->
                </div>
       
        <!-- Detailed Booking Information Card -->
        <div class="border-2 border-kreen-gold rounded-xl p-6 space-y-8" style="background: linear-gradient(98.07deg, #000000 0%, #2E2E2E 25.48%, #000000 50.48%, #464646 81.73%, #040404 100%);">
            <h3 class="text-white text-xl font-bold">Booking Details</h3>
            
            <!-- Venue Info -->
            <div class="flex items-center space-x-3">
                <div class="w-6 h-6 flex items-center justify-center flex-shrink-0">
                    <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <span class="text-white font-medium">{{ $order->venue_name }}</span>
            </div>

            <!-- Date -->
            <div class="flex items-center space-x-3">
                <div class="w-6 h-6 flex items-center justify-center flex-shrink-0">
                    <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <span class="text-white font-medium">{{ \Carbon\Carbon::parse($order->date)->format('l, d F Y') }}</span>
            </div>

            @foreach ($orderDetails as $index => $ticket)
            @for ($i = 0; $i < $ticket->qty; $i++)
            <div class="bg-white/20 rounded-lg p-6 mb-6">
                <div class="flex items-center space-x-3 mb-4">
                    <div class="w-6 h-6 flex items-center justify-center flex-shrink-0">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2 6a2 2 0 012-2h12a2 2 0 012 2v2a2 2 0 100 4v2a2 2 0 01-2 2H4a2 2 0 01-2-2v-2a2 2 0 100-4V6z"/>
                        </svg>
                    </div>
                    <span class="text-white font-medium">
                        Guest {{ ($loop->parent->index ?? $index) * $ticket->qty + $i + 1 }} : {{ $ticket->nama_tiket ?? 'Ticket' }}
                    </span>
                </div>

                <!-- QR Code -->
                    @if ($order->status == 1)
                        <div class="text-center mb-4">
                            <div class="qr-code inline-block">
                                <div class="qr-pattern">
                                    <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data={{ urlencode($ticket->id) }}" alt="QR Code">
                                </div>
                            </div>
                            <p class="text-gray-300 text-sm mt-2">QR ID : {{ $ticket->id }}</p>
                        </div>
                    @endif

                <div class="ticket-separator"></div>

                <!-- Guest Details -->
                <div class="space-y-3 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-400">Full Name</span>
                        <span class="text-white">{{ $ticket->full_name }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Email</span>
                        <span class="text-white">{{ $ticket->email }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Phone Number</span>
                        <span class="text-white">{{ $ticket->phone }}</span>
                    </div>
                </div>
            </div>
            @endfor
        @endforeach


            <!-- Payment Detail -->
            <div>
                <h4 class="text-white font-semibold mb-4">Payment Detail</h4>
                
                <div class="space-y-3 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-400">Booking Code</span>
                        <span class="text-white font-mono">{{ $order->invoice_number }}</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-400">Date</span>
                        <span class="text-white">{{ \Carbon\Carbon::parse($order->created_at)->format('l, d F Y') }}</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-400">{{ $order->venue_name }}(x{{ $totalTickets }})</span>
                        <span class="text-white">Rp {{ number_format($order->total_price, 0, ',', '.') }}</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-400">Service Fee</span>
                        <span class="text-white">Rp {{ number_format($order->fees, 0, ',', '.') }}</span>
                    </div>
                    
                    <hr class="border-gray-600">
                    
                    <div class="flex justify-between">
                        <span class="text-white font-medium">Total Payment</span>
                        <span class="text-kreen-gold font-bold text-lg">Rp {{ number_format($order->total_amount, 0, ',', '.') }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
    function goBack() {
        window.history.back();
    }

    // Optional: Smooth scroll to top on page load
    document.addEventListener('DOMContentLoaded', function() {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    });
</script>
<script>
    // Function to view order details
    function viewOrder(orderId) {
        // Redirect to the order detail page
        window.location.href = `/vip/my-order/${orderId}`;
    }
</script>
<script>
    // Function to handle the back button
    function goBack() {
        window.history.back();
    }
    // Optional: Smooth scroll to top on page load
    document.addEventListener('DOMContentLoaded', function() {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    });
</script>
@endpush
