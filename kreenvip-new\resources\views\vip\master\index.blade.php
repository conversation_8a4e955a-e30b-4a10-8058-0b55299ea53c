@extends('vip.app')

@section('title', 'KREEN VIP - Exclusive Nightlife Experience')

@section('content')
    <!-- Container dengan Background Image untuk Hero + Explore Section -->
    <div class="hero-explore-bg-container">
        <!-- Hero Slider Section -->
        <section class="hero-explore-content mt-[50px] py-8">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="rounded-xl overflow-hidden">
                    <div class="splide hero-splide" id="hero-slider">
                        <div class="splide__track">
                            <ul class="splide__list">
                                @foreach ($banners as $banner)
                                    <!-- Slide 1 -->
                                    <li class="splide__slide kreen-golden-border">
                                        <div class="hero-slide"
                                            style="background-image: url('{{ strpos($banner->gambar, 'http') === 0 ? $banner->gambar : asset('image/' . $banner->gambar) }}');">
                                            <div class="hero-content">
                                                <h2 class="text-4xl md:text-5xl font-bold mb-4">
                                                    {{ $banner->header }}
                                                </h2>
                                                <p class="text-xl md:text-2xl text-gray-200 mb-2">
                                                    {{ $banner->narasi }}
                                                </p>
                                            </div>
                                        </div>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Explore Nights Section - Updated Layout -->
        <section id="nights-section" class="hero-explore-content py-20" style="display: none;">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <!-- Section Header -->
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-3xl md:text-4xl font-bold text-white">Explore Nights</h2>

                    <!-- See More Link -->
                    <a href="{{ route('venue.exploreNights') }}"
                        class="text-kreen-gold hover:text-yellow-400 transition-colors font-semibold flex items-center group">
                        See More
                        <svg class="w-5 h-5 ml-2 transform group-hover:translate-x-1 transition-transform" fill="none"
                            stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                </div>

                <!-- Location Dropdown - Moved below title -->
                <div class="mb-8">
                    <div class="relative inline-block">
                        <select
                            class="bg-black text-white border border-gray-600 px-4 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-kreen-gold appearance-none pr-8"
                            onchange="handleLocationChange('night')" data-type="night" id="night-city">
                            <option value="" class="text-black dark:text-white">Filter City</option>
                            @foreach ($venue_cities as $venue_city)
                                <option class="text-black dark:text-white" value="{{ $venue_city->id }}">{{ $venue_city->name }}</option>
                            @endforeach
                        </select>

                        <svg class="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-white pointer-events-none"
                            fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                clip-rule="evenodd" />
                        </svg>
                    </div>
                </div>

                <!-- Venues Grid - No Cards, Golden Border Images -->
                <div id="venues-data-from-ajax">
                </div>
            </div>
        </section>
    </div>

    <!-- Events Section - Update grid class -->
    <section id="events-section" class="py-20 bg-gradient-to-b from-black to-gray-900" style="display: none;">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Section Header -->
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-3xl md:text-4xl font-bold text-white">Events</h2>

                <!-- See More Link -->
                <a href="{{ route('event.exploreEvents') }}"
                    class="text-kreen-gold hover:text-yellow-400 transition-colors font-semibold flex items-center group">
                    See More
                    <svg class="w-5 h-5 ml-2 transform group-hover:translate-x-1 transition-transform" fill="none"
                        stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </a>
            </div>

            <!-- Location Dropdown -->
            <div class="mb-8">
                <div class="relative inline-block">
                    <select
                        class="bg-black text-white border border-gray-600 px-4 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-kreen-gold appearance-none pr-8"
                        data-type="event"
                        id="event-city"
                        onchange="handleLocationChange('event')"
                        style="background-color: #000; color: #fff;">
                        <option value="" class="text-black dark:text-white">Filter City</option>
                        @foreach ($event_cities as $event_city)
                            <option value="{{ $event_city->id }}" class="text-black dark:text-white">
                                {{ $event_city->name }}
                            </option>
                        @endforeach
                    </select>

                    <!-- Custom Arrow Icon -->
                    <svg class="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-white pointer-events-none"
                        fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd"
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                            clip-rule="evenodd" />
                    </svg>
                </div>
            </div>


            <!-- Events Grid - Using specific class -->
            <div id="events-data-from-ajax">
            </div>
        </div>
    </section>

    <!-- Meet The DJs Section - Using specific class -->
    @if($djs->count() > 0)
    <section class="py-20 bg-gradient-to-b from-gray-900 to-black">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Section Header -->
            <div class="flex items-center justify-between mb-8">
                <h2 class="text-3xl md:text-4xl font-bold text-white">Meet The DJs</h2>

                <!-- See More Link -->
                <a href="{{ route('dj.listDj') }}"
                    class="text-kreen-gold hover:text-yellow-400 transition-colors font-semibold flex items-center group">
                    See More
                    <svg class="w-5 h-5 ml-2 transform group-hover:translate-x-1 transition-transform" fill="none"
                        stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </a>
            </div>

            <!-- DJs Grid - Using specific class -->
            <div class="djs-grid">
                <!-- DJ cards remain the same -->
                @foreach ($djs as $dj)
                    <!-- DJ 1 -->
                    <div class="dj-card group cursor-pointer">
                        <div class="dj-image"
                            style="background-image: url('{{ strpos($dj->gambar, 'http') === 0 ? $dj->gambar : asset($dj->gambar) }}');">
                        </div>
                        <div class="dj-info">
                            <h3 class="text-white font-bold text-lg text-center">{{ $dj->nama }}</h3>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif
    @include('vip.footer') <!-- CTA Section -->

    <!-- CTA Section (existing content) -->
    <!-- <section class="py-20 bg-kreen-dark">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
                Ready to Experience the <span class="text-kreen-gold">VIP Life</span>?
            </h2>
            <p class="text-xl text-gray-400 mb-8">
                Join thousands of members who have elevated their nightlife experience
            </p>
            <a href="{{ route('auth.login') ?? '#' }}"
                class="inline-block bg-kreen-gold hover:bg-kreen-gold-hover text-black font-bold px-12 py-4 rounded-lg transition-all duration-300 transform hover:scale-105 hover:shadow-xl">
                Become a Member Today
            </a>
        </div>
    </section> -->

@endsection

@push('scripts')
    <!-- Splide JS -->
    <script src="https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/js/splide.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Hero Slider
            new Splide('#hero-slider', {
                type: 'loop',
                autoplay: true,
                interval: 5000,
                pauseOnHover: true,
                arrows: true,
                pagination: true,
                speed: 800,
                easing: 'ease-in-out',
            }).mount();

            // Venue item click handlers
            document.querySelectorAll('.venue-item-simple').forEach(item => {
                item.addEventListener('click', function() {
                    console.log('Venue clicked:', this.querySelector('h3').textContent);
                });
            });

            // Event card click handlers
            document.querySelectorAll('.event-card').forEach(card => {
                card.addEventListener('click', function() {
                    console.log('Event clicked:', this.querySelector('h3').textContent);
                });
            });

            // DJ card click handlers
            document.querySelectorAll('.dj-card').forEach(card => {
                card.addEventListener('click', function() {
                    console.log('DJ clicked:', this.querySelector('h3').textContent);
                });
            });

            // Sticky Navbar Scroll Effect
            const navbar = document.querySelector('.navbar, header, nav');
            if (navbar) {
                window.addEventListener('scroll', function() {
                    if (window.scrollY > 50) {
                        navbar.classList.add('scrolled');
                    } else {
                        navbar.classList.remove('scrolled');
                    }
                });
            }
        });

        $('select[data-type="event"]').select2({
            width: '100%',
            placeholder: 'Filter City'
        });

        $('select[data-type="night"]').select2({
            width: '100%',
            placeholder: 'Filter City'
        });

        function handleLocationChange(type) {
            const selectedCityId = $(`select[data-type="${type}"]`).val();
            if (type === "night") {
                $.ajax({
                    url: '/ajax/explore_nights',
                    type: 'GET',
                    dataType: 'json',
                    data: {
                        id_kota: selectedCityId,
                        page: 1,
                        limit: 4
                    },
                    success: function(response) {
                        if(response.count == 0) {
                            $('#nights-section').hide();
                        } else {
                            $('#nights-section').show();
                        }
                        $('#venues-data-from-ajax').html(response.view);
                    },
                    error: function(xhr, status, error) {
                        console.log(error);
                    }
                });
            } else if (type === "event") {
                $.ajax({
                    url: '/ajax/explore_events',
                    type: 'GET',
                    dataType: 'json',
                    data: {
                        id_kota: selectedCityId,
                        page: 1,
                        limit: 4
                    },
                    success: function(response) {
                        if(response.count == 0) {
                            $('#events-section').hide();
                        } else {
                            $('#events-section').show();
                        }
                        $('#events-data-from-ajax').html(response.view);
                    },
                    error: function(xhr, status, error) {
                        console.log(error);
                    }
                });
            }
        }

        $(document).ready(function() {
            handleLocationChange('night');
            handleLocationChange('event');
        });
    </script>
@endpush
