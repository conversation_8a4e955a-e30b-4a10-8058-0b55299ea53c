<?php
namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Illuminate\Support\Facades\DB;
use Ramsey\Uuid\Uuid;
use App\Http\Controllers\Controller;

class EventVenueImportController extends Controller
{
    // Show the upload form
    public function showUploadForm()
    {
        return view('excel.event_venue_upload');
    }

    // Handle Excel file upload and import
    public function importExcel(Request $request)
    {
        try {
            // Validate the uploaded file
            $request->validate([
                'file' => 'required|mimes:xlsx,xls'
            ]);

            // Load the uploaded Excel file
            $file = $request->file('file');
            $spreadsheet = IOFactory::load($file->getRealPath());
            $sheet = $spreadsheet->getActiveSheet();
            $rows = $sheet->toArray();

            // Database connections
            $kreenvipDB = DB::connection('kreenvip_staging');
            $kreenProductionDB = DB::connection('kreen_production_online');

            // Skip header row and process each row
            foreach (array_slice($rows, 1) as $row) {
                // Extract data for tbl_bar_event
                $eventData = [
                    'id' => Uuid::uuid4()->toString(),
                    'id_venue' => $this->getOrCreateVenue($row, $kreenvipDB, $kreenProductionDB),
                    'nama' => $row[0] ?? null, // Event Name
                    'tanggal' => $this->formatDate($row[1] ?? null), // Event Date
                    'slug' => $this->generateSlug($row[0] ?? null),
                    'gambar' => $row[2] ?? null, // Event Image URL
                    'deskripsi' => $row[3] ?? null, // Event Description
                    'lokasi' => $row[4] ?? null, // Event Location
                    'id_negara' => $this->getLocationId($kreenProductionDB, 'tbl_adm_country', $row[5] ?? null),
                    'id_provinsi' => $this->getLocationId($kreenProductionDB, 'tbl_adm_province', $row[6] ?? null),
                    'id_kota' => $this->getLocationId($kreenProductionDB, 'tbl_adm_regency', $row[7] ?? null),
                    'id_kelurahan' => $this->getLocationId($kreenProductionDB, 'tbl_adm_district', $row[8] ?? null),
                    'flag_aktif' => $row[9] ?? '1', // Event Active Flag
                    'created_at' => now(),
                    'updated_at' => now()
                ];

                // Insert into tbl_bar_event
                $kreenvipDB->table('tbl_bar_event')->insert($eventData);
            }

            return redirect()->back()->with('success', 'Data imported successfully');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error importing data: ' . $e->getMessage());
        }
    }

    private function getOrCreateVenue($row, $kreenvipDB, $kreenProductionDB)
    {
        // Check if venue exists by name
        $venueName = $row[10] ?? null; // Venue Name from Excel
        if (!$venueName) {
            return null;
        }

        $existingVenue = $kreenvipDB->table('tbl_bar_venue')
            ->where('nama', $venueName)
            ->first();

        if ($existingVenue) {
            return $existingVenue->id;
        }

        // Create new venue
        $venueData = [
            'id' => Uuid::uuid4()->toString(),
            'id_merchant' => $row[11] ?? null, // Merchant ID
            'nama' => $venueName,
            'informasi' => $row[12] ?? null, // Venue Information
            'banner' => $row[13] ?? null, // Venue Banner URL
            'currency' => $row[14] ?? 'IDR', // Venue Currency
            'lat' => $row[15] ?? null, // Venue Latitude
            'lng' => $row[16] ?? null, // Venue Longitude
            'id_negara' => $this->getLocationId($kreenProductionDB, 'tbl_adm_country', $row[5] ?? null),
            'id_propinsi' => $this->getLocationId($kreenProductionDB, 'tbl_adm_province', $row[6] ?? null),
            'id_kota' => $this->getLocationId($kreenProductionDB, 'tbl_adm_regency', $row[7] ?? null),
            'id_kelurahan' => $this->getLocationId($kreenProductionDB, 'tbl_adm_district', $row[8] ?? null),
            'alamat' => $row[17] ?? null, // Venue Address
            'slug' => $this->generateSlug($venueName),
            'flag_aktif' => $row[18] ?? '0', // Venue Active Flag
            'username_ig' => $row[19] ?? null, // Venue Instagram Username
            'no_wa' => $row[20] ?? null, // Venue WhatsApp Number
            'created_at' => now(),
            'updated_at' => now()
        ];

        $kreenvipDB->table('tbl_bar_venue')->insert($venueData);
        return $venueData['id'];
    }

    private function getLocationId($db, $table, $name)
    {
        if (!$name) {
            return null;
        }

        $location = $db->table($table)
            ->where('name', $name)
            ->select('id')
            ->first();

        return $location ? $location->id : null;
    }

    private function formatDate($date)
    {
        if (!$date) {
            return null;
        }

        try {
            if (is_numeric($date)) {
                return \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($date)->format('Y-m-d');
            }
            return date('Y-m-d', strtotime($date));
        } catch (\Exception $e) {
            return null;
        }
    }

    private function generateSlug($name)
    {
        if (!$name) {
            return null;
        }
        return str_replace(' ', '-', strtolower(trim($name)));
    }
}
?>