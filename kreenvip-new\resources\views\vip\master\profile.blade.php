@extends('vip.app')

@section('title', '<PERSON><PERSON><PERSON> - KREEN VIP')

@push('styles')
<link href="https://cdn.jsdelivr.net/npm/lightgallery@2.7.1/css/lightgallery.css" rel="stylesheet">
<link href="https://cdn.jsdelivr.net/npm/lightgallery@2.7.1/css/lg-zoom.css" rel="stylesheet">
<link href="https://cdn.jsdelivr.net/npm/lightgallery@2.7.1/css/lg-thumbnail.css" rel="stylesheet">

<style>
/* Full width section */
.full-width-section {
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    padding-left: calc(50vw - 50%);
    padding-right: calc(50vw - 50%);
}

/* Hero section styling */
.hero-section {
    background-size: cover;
    background-position: center;
    position: relative;
    height: 60vh;
    min-height: 400px;
}

.hero-section::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(0,0,0,0.6), rgba(0,0,0,0.8));
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
}

/* Event cards */
.event-card {
    background: linear-gradient(135deg, rgba(30, 30, 30, 0.9), rgba(50, 50, 50, 0.9));
    border: 1px solid rgba(212, 175, 55, 0.3);
    border-radius: 0.75rem;
    padding: 1rem;
    transition: all 0.3s ease;
}

.event-card:hover {
    border-color: #D4AF37;
    transform: translateY(-2px);
}

.event-image {
    width: 80px;
    height: 80px;
    border-radius: 0.75rem;
    object-fit: cover;
}

/* Profile image */
.profile-image {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #D4AF37;
}

/* Social media icons */
.social-icon {
    width: 24px;
    height: 24px;
    fill: #9CA3AF;
    transition: fill 0.3s ease;
}

.social-icon:hover {
    fill: #D4AF37;
}

/* Gallery grid */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.gallery-item {
    aspect-ratio: 1;
    border-radius: 0.5rem;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.3s ease;
    position: relative;
}

.gallery-item:hover {
    transform: scale(1.05);
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* See All Photos overlay */
.see-all-overlay {
    position: absolute;
    inset: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    items-center: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
    text-align: center;
    transition: background-color 0.3s ease;
}

.see-all-overlay:hover {
    background: rgba(0, 0, 0, 0.8);
}

/* Live shows */
.live-show-card {
    background: linear-gradient(135deg, rgba(30, 30, 30, 0.9), rgba(50, 50, 50, 0.9));
    border: 1px solid rgba(212, 175, 55, 0.3);
    border-radius: 0.75rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.live-show-card:hover {
    border-color: #D4AF37;
}

.live-show-image {
    width: 100%;
    height: 120px;
    object-fit: cover;
}

/* Mobile responsive adjustments */
@media (max-width: 1024px) {
    .gallery-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.75rem;
    }
}

/* Mobile upcoming events positioning */
.mobile-upcoming-events {
    order: 10;
}

@media (max-width: 1024px) {
    .desktop-layout {
        display: flex;
        flex-direction: column;
    }
    
    .mobile-upcoming-events {
        order: 10;
        margin-top: 3rem;
    }
}
</style>
@endpush

@section('content')
<!-- Hero Section -->
<section class="full-width-section hero-section" style="background-image: url('https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');">
    <div class="hero-content">
        <h1 class="text-white text-6xl md:text-8xl font-bold mb-4 tracking-wider">YASMIN</h1>
        <h2 class="text-white text-2xl md:text-3xl font-medium mb-2">DJ Yasmin</h2>
        <p class="text-gray-300 text-lg">Jakarta, Indonesia</p>
    </div>
</section>

<!-- Main Content -->
<section class="full-width-section py-12 bg-gradient-to-b from-black to-gray-900">
    <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <!-- Desktop Layout - Two Column Layout -->
        <div class="desktop-layout grid grid-cols-1 lg:grid-cols-2 gap-12 mb-12">
            <!-- Left Column - Upcoming Shows (Desktop) / Hidden on Mobile -->
            <div class="hidden lg:block">
                <h3 class="text-white text-xl font-bold mb-6 flex items-center">
                    <span class="text-red-500 mr-2">🔥</span>
                    Upcoming Shows
                </h3>
                <div class="space-y-6">
                    <div class="event-card">
                        <div class="flex items-center space-x-4">
                            <img src="https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80" 
                                 alt="Princess Joana & DJ Yasmin" 
                                 class="event-image">
                            <div class="flex-1">
                                <h4 class="text-white font-medium text-lg mb-1">Princess Joana & DJ Yasmin</h4>
                                <p class="text-gray-400 text-sm">The H Club SCBD</p>
                                <p class="text-gray-400 text-sm">19 Jun 2025</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="event-card">
                        <div class="flex items-center space-x-4">
                            <img src="https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80" 
                                 alt="SoundFest 2025" 
                                 class="event-image">
                            <div class="flex-1">
                                <h4 class="text-white font-medium text-lg mb-1">SoundFest 2025</h4>
                                <p class="text-gray-400 text-sm">Gambir Expo, Jakarta</p>
                                <p class="text-gray-400 text-sm">18 Jun 2025</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column - Social Media & Streaming Links (Desktop Only) -->
            <div class="space-y-8 hidden lg:block">
                <!-- Social Media -->
                <div>
                    <h3 class="text-white text-xl font-bold mb-6">Social Media</h3>
                    <div class="space-y-4">
                        <a href="#" class="flex items-center space-x-3 text-gray-400 hover:text-kreen-gold transition-colors">
                            <svg class="social-icon" viewBox="0 0 24 24">
                                <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                            </svg>
                            <span class="text-sm">dj_yasmin</span>
                        </a>
                        
                        <a href="#" class="flex items-center space-x-3 text-gray-400 hover:text-kreen-gold transition-colors">
                            <svg class="social-icon" viewBox="0 0 24 24">
                                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                            </svg>
                            <span class="text-sm">DJ Yasmin</span>
                        </a>
                    </div>
                </div>

                <!-- Streaming Links -->
                <div>
                    <h3 class="text-white text-xl font-bold mb-6">Streaming Links</h3>
                    <div class="space-y-4">
                        <a href="#" class="flex items-center space-x-3 text-gray-400 hover:text-kreen-gold transition-colors">
                            <svg class="social-icon" viewBox="0 0 24 24">
                                <path d="M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.779-.179-.899-.539-.12-.421.18-.78.54-.9 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.301 1.02zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.42 1.56-.299.421-1.02.599-1.559.3z"/>
                            </svg>
                            <span class="text-sm">DJ Yasmin</span>
                        </a>
                        
                        <a href="#" class="flex items-center space-x-3 text-gray-400 hover:text-kreen-gold transition-colors">
                            <svg class="social-icon" viewBox="0 0 24 24">
                                <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                            </svg>
                            <span class="text-sm">DJ Yasmin</span>
                        </a>
                        
                        <a href="#" class="flex items-center space-x-3 text-gray-400 hover:text-kreen-gold transition-colors">
                            <svg class="social-icon" viewBox="0 0 24 24">
                                <path d="M7.443 0C3.333 0 0 3.333 0 7.443v9.114C0 20.667 3.333 24 7.443 24h9.114C20.667 24 24 20.667 24 16.557V7.443C24 3.333 20.667 0 16.557 0H7.443zM12 6c3.315 0 6 2.685 6 6s-2.685 6-6 6-6-2.685-6-6 2.685-6 6-6zm0 2c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4z"/>
                            </svg>
                            <span class="text-sm">Fahria Yasmin</span>
                        </a>
                        
                        <a href="#" class="flex items-center space-x-3 text-gray-400 hover:text-kreen-gold transition-colors">
                            <svg class="social-icon" viewBox="0 0 24 24">
                                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z"/>
                            </svg>
                            <span class="text-sm">DJ Yasmin</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Section - Full Width -->
        <div class="mb-12">
            <h3 class="text-white text-xl font-bold mb-6">Profile</h3>
            <div class="flex flex-col lg:flex-row lg:items-start space-y-6 lg:space-y-0 lg:space-x-8">
                <div class="lg:w-1/3">
                    <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" 
                         alt="DJ Yasmin Profile" 
                         class="w-full h-80 object-cover rounded-lg">
                    <h4 class="text-white text-xl font-bold mt-4 mb-2">DJ Yasmin</h4>
                </div>
                <div class="lg:w-2/3 text-gray-300 text-sm leading-relaxed space-y-4">
                    <p>A Titan when it comes to Indonesia's Dance Scene. Blessing hip venues across Indonesia, in Singapore, Malaysia, Philippines, Thailand, Macau, Cambodia, Switzerland and even Amsterdam.</p>
                    
                    <p>Yasmin has also won an award as Best DJ at 2015 Paranoia Awards and entered the AMI (Anugerah Musik Indonesia) #3 Indonesia DJ Of The Year.</p>
                    
                    <p>The model-turned-DJ started at the age of 19 by joining one of the biggest DJ Management companies for her talent. She also has played at Djakarta Warehouse Project, which for those reasons, makes her one of the most In-Demand artists in the scene. Her inspirations come from Deep House, Melodic Techno, Electronica, to Cyberpunk, and Anime Van Buuren.</p>
                    
                    <p>Yasmin is a force to be reckoned with and there is no holding her back.</p>
                </div>
            </div>
        </div>

        <!-- Gallery Section -->
        <div class="mb-12">
            <h3 class="text-white text-xl font-bold mb-6">Gallery</h3>
            <div id="lightgallery" class="gallery-grid">
                <div class="gallery-item" data-src="https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80">
                    <img src="https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="DJ Performance 1">
                </div>
                <div class="gallery-item" data-src="https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80">
                    <img src="https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="DJ Performance 2">
                </div>
                <div class="gallery-item cursor-pointer" onclick="showAllPhotos()">
                    <img src="https://images.unsplash.com/photo-1598300042247-d088f8ab3a91?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="DJ Performance 3">
                    <div class="see-all-overlay">
                        <span>See All Photos</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Live Shows Section -->
        <div class="mb-12 hidden md:block">
            <h3 class="text-white text-xl font-bold mb-6">Live Shows</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="live-show-card">
                    <img src="https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" 
                         alt="Live Show 1" 
                         class="live-show-image">
                    <div class="p-4">
                        <h4 class="text-white font-medium text-sm mb-1">Face 2 Face Concept</h4>
                        <p class="text-gray-400 text-xs mb-2">The H Club SCBD • Jun 19, 2025</p>
                        <p class="text-gray-300 text-xs">Experience the ultimate house music journey with Princess Joana & DJ Yasmin.</p>
                    </div>
                </div>
                
                <div class="live-show-card">
                    <img src="https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" 
                         alt="Live Show 2" 
                         class="live-show-image">
                    <div class="p-4">
                        <h4 class="text-white font-medium text-sm mb-1">SoundFest 2025</h4>
                        <p class="text-gray-400 text-xs mb-2">Jakarta Convention Center • Jul 15, 2025</p>
                        <p class="text-gray-300 text-xs">Indonesia's biggest electronic music festival featuring top international and local DJs.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile Upcoming Events Section (Appears at bottom on mobile) -->
        <div class="mobile-upcoming-events lg:hidden">
            <h3 class="text-white text-xl font-bold mb-6 flex items-center">
                <span class="text-red-500 mr-2">🔥</span>
                Upcoming Shows
            </h3>
            <div class="space-y-6">
                <div class="event-card">
                    <div class="flex items-center space-x-4">
                        <img src="https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80" 
                             alt="Princess Joana & DJ Yasmin" 
                             class="event-image">
                        <div class="flex-1">
                            <h4 class="text-white font-medium text-lg mb-1">Princess Joana & DJ Yasmin</h4>
                            <p class="text-gray-400 text-sm">The H Club SCBD</p>
                            <p class="text-gray-400 text-sm">19 Jun 2025</p>
                        </div>
                    </div>
                </div>
                
                <div class="event-card">
                    <div class="flex items-center space-x-4">
                        <img src="https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80" 
                             alt="SoundFest 2025" 
                             class="event-image">
                        <div class="flex-1">
                            <h4 class="text-white font-medium text-lg mb-1">SoundFest 2025</h4>
                            <p class="text-gray-400 text-sm">Gambir Expo, Jakarta</p>
                            <p class="text-gray-400 text-sm">18 Jun 2025</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</section>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/lightgallery@2.7.1/lightgallery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/lightgallery@2.7.1/plugins/zoom/lg-zoom.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/lightgallery@2.7.1/plugins/thumbnail/lg-thumbnail.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function () {
    // Initialize LightGallery
    lightGallery(document.getElementById('lightgallery'), {
        plugins: [lgZoom, lgThumbnail],
        speed: 500,
        thumbnail: true,
        animateThumb: false,
        zoomFromOrigin: false,
        allowMediaOverlap: true,
        toggleThumb: true
    });
    
    console.log('DJ Profile page loaded with LightGallery');
});

// Show all photos function
function showAllPhotos() {
    // Create a modal or redirect to full gallery page
    alert('Opening full gallery...');
    // You can implement a modal here or redirect to a full gallery page
}
</script>
@endpush
