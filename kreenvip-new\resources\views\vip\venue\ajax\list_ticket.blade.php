@forelse ($tickets as $idx => $ticket)
    <div class="border-2 border-kreen-gold rounded-2xl p-6 bg-transparent tickets" id="ticket-{{ $idx }}"
        data-price="{{ $ticket->harga }}" data-stock="{{ $ticket->stok_tersedia }}" data-id-ticket="{{ $ticket->id }}" data-ticket-name="{{ $ticket->nama_tiket }}">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-white font-semibold text-lg">{{ $ticket->nama_tiket }}</h3>
            <div class="flex items-center shrink-0">
                <button
                    type="button"
                    class="w-8 h-8 shrink-0 border border-gray-400 rounded flex items-center justify-center text-white hover:bg-gray-700"
                    onclick="decreaseQuantity({{ $idx }})">
                    <span>-</span>
                </button>
                <input type="number" min="0" max="{{ $ticket->stok_tersedia }}" class="quantity-input shrink-0 text-white text-center w-24"
                    id="quantity-{{ $idx }}" value="0"  oninput="countPrice({{ $idx }})">
                <button
                    type="button"
                    class="w-8 h-8 shrink-0 border border-gray-400 rounded flex items-center justify-center text-white hover:bg-gray-700"
                    onclick="increaseQuantity({{ $idx }})">
                    <span>+</span>
                </button>
            </div>
        </div>

        <div class="mb-4">
        </div>

        <div class="text-right">
            <p class="text-kreen-gold font-bold text-xl" id="price-{{ $idx }}">Rp
                {{ number_format($ticket->harga, 0, ',', '.') }}</p>
        </div>
    </div>

    <script>
        function increaseQuantity(idx) {
            const quantitySpan = $('#quantity-' + idx);
            const stock = parseInt($('#ticket-' + idx).data('stock'));
            let quantity = parseInt(quantitySpan.val());
            if (quantity < stock) {
                quantity++;
                quantitySpan.val(quantity);
            } else {
                quantitySpan.val(stock);
            }
            countPrice(idx);
        }

        function decreaseQuantity(idx) {
            const quantitySpan = $('#quantity-' + idx);
            const stock = parseInt($('#ticket-' + idx).data('stock'));
            let quantity = parseInt(quantitySpan.val());
            if (quantity > 0) {
                quantity--;
                quantitySpan.val(quantity);
            }
            countPrice(idx);
        }

        function countPrice(idx) {
            const quantitySpan = $('#quantity-' + idx);
            const stock = parseInt($('#ticket-' + idx).data('stock'));
            if (parseInt(quantitySpan.val()) > stock) {
                quantitySpan.val(stock);
            }
            if(parseInt(quantitySpan.val()) < 0 || quantitySpan.val() == '') {
                quantitySpan.val(0);
            }
            const ticket = $('#ticket-' + idx);

            const price = parseInt(ticket.data('price'));
            const ticketName = ticket.data('ticket-name');
            const quantity = parseInt(quantitySpan.val());

            // Hapus semua kartu tiket yang ada
            $('#selected-ticket-container .ticket-card').remove();

            // Kumpulkan semua tiket yang dipilih dan buat kartu terpisah
            const selectedTickets = [];
            $('.tickets').each(function() {
                const qty = parseInt($(this).find('.quantity-input').val());
                if (qty > 0) {
                    const name = $(this).data('ticket-name');
                    const pricePerTicket = parseInt($(this).data('price'));
                    const totalPricePerTicket = qty * pricePerTicket;
                    selectedTickets.push({ name, qty, totalPricePerTicket });
                }
            });

            // Tambahkan kartu untuk setiap tiket yang dipilih
            if (selectedTickets.length > 0) {
                $('#selected-ticket-default').hide();
                selectedTickets.forEach(ticket => {
                    const ticketCard = `
                        <div class="ticket-card">
                            <div class="flex items-center space-x-3">
                                <div class="w-6 h-6 flex items-center justify-center flex-shrink-0">
                                    <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M2 6a2 2 0 012-2h12a2 2 0 012 2v2a2 2 0 100 4v2a2 2 0 01-2 2H4a2 2 0 01-2-2v-2a2 2 0 100-4V6z" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-gray-400 text-sm">
                                    ${ticket.name} - x${ticket.qty} ticket(s)<br>
                                    Rp ${ticket.totalPricePerTicket.toLocaleString('id-ID')}
                                    </p>
                                </div>
                            </div>
                        </div>
                    `;
                    $('#selected-ticket-container').append(ticketCard);
                });
            } else {
                $('#selected-ticket-default').show();
            }

            // Hitung total
            // const allQuantity = [...document.querySelectorAll('.quantity-input')].reduce((acc, curr) => acc + parseInt(curr.value), 0);
            // const allPrice = [...document.querySelectorAll('.tickets')].reduce((acc, curr) => acc + (parseInt(curr.querySelector('.quantity-input').value) * parseInt(curr.getAttribute('data-price'))), 0);

            const allQuantity = [...document.querySelectorAll('.quantity-input')].reduce((acc, curr) => acc + (parseInt(curr.value) || 0), 0);
            const allPrice = [...document.querySelectorAll('.tickets')].reduce((acc, curr) => {
                const qty = parseInt(curr.querySelector('.quantity-input').value) || 0;
                const price = parseInt(curr.getAttribute('data-price')) || 0;
                return acc + (qty * price);
            }, 0);

            const totalText = document.querySelector('#total-text');
            const totalPriceText = document.querySelector('#total-price');
            totalText.textContent = `Total (${allQuantity} ticket${allQuantity !== 1 ? 's' : ''})`;
            totalPriceText.textContent = `Rp ${allPrice.toLocaleString('id-ID')}`;

            console.log(allPrice);
        }

        // Event delegation untuk input quantity
        $(document).on('change', '.quantity-input', function() {
            const idx = $(this).attr('id').replace('quantity-', '');
            countPrice(idx);
        });

        // Panggil ulang saat data AJAX dimuat
        $(document).ajaxComplete(function() {
            $('.quantity-input').each(function() {
                const idx = $(this).attr('id').replace('quantity-', '');
                countPrice(idx);
            });
        });


    </script>
@empty
    <div class="text-center">
        Belum ada tiket
    </div>
@endforelse
