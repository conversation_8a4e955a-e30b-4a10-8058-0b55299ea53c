@extends('vip.app')

@section('title', 'Forgot Password - KREEN VIP')

@section('content')
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gradient-to-br via-gray-900 to-kreen-dark">
    <div class="max-w-md w-full space-y-8">
        <div class="kreen-card kreen-golden-border p-8 backdrop-blur-sm">
            <!-- Logo -->
            <div class="mb-8">
                <div class="flex items-center justify-left mb-4">
                    <div class="w-[120px] h-[60px]"> <!-- Ukuran container diperbesar -->
                        <img src="{{ asset('image/kreenvip/kreenvip.png') }}" alt="KREEN VIP" class="h-12 w-auto object-contain" />
                    </div>
                </div>
                <h2 class="text-xl font-semibold text-white">Forgot Your Password</h2>
            </div>

              <!-- Gradient line -->
              <div class="mt-1 w-full h-[1px] bg-gradient-to-r from-[#A37A1D] via-[#FFF0C0] to-[#281A00]"></div>

            <!-- Success Message -->
            @if (session('status'))
                <div class="mb-6 p-4 bg-green-900 bg-opacity-50 border border-green-500 rounded-lg">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                        <span class="text-green-400 text-sm">{{ session('status') }}</span>
                    </div>
                </div>
            @endif

            <!-- Forgot Password Form -->
            <form method="POST" action="{{ route('auth.forgotPassword.process') }}" class="space-y-6">
                @csrf

                <!-- Email Field -->
                <div>
                    <label for="email" class="block text-sm font-medium text-white mb-2">
                        Email <span class="text-red-500">*</span>
                    </label>
                    <input
                        id="email"
                        name="email"
                        type="email"
                        required
                        class="w-full px-4 py-3 bg-white text-black rounded-lg border-0 focus:ring-2 focus:ring-kreen-gold focus:outline-none transition-all @error('email') ring-2 ring-red-500 @enderror"
                        placeholder="Enter your email"
                        value="{{ old('email') }}"
                    >
                    @error('email')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Send Email Button -->
                <button type="submit" id="sendEmailBtn" class="w-full bg-kreen-gold hover:bg-kreen-gold-hover text-black font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105 relative">
                    <span class="btn-text">Reset Password</span>
                    <span class="btn-loading hidden">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-black inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Sending...
                    </span>
                </button>

                <!-- Sign In Link -->
                <div class="text-center">
                    <span class="text-gray-400">Remember your password? </span>
                    <a href="{{ route('auth.login') ?? '#' }}" class="text-kreen-gold hover:text-yellow-400 transition-colors font-semibold">Login Now!</a>
                </div>

                <!-- Help Text -->
                <!-- <div class="text-center text-xs text-gray-400">
                    <p class="mb-2">Having trouble? Contact our support team</p>
                    <a href="mailto:<EMAIL>" class="text-kreen-gold hover:text-yellow-400 transition-colors"><EMAIL></a>
                </div> -->
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const sendBtn = document.getElementById('sendEmailBtn');
    const btnText = sendBtn.querySelector('.btn-text');
    const btnLoading = sendBtn.querySelector('.btn-loading');

    form.addEventListener('submit', function(e) {
        // Show loading state
        btnText.classList.add('hidden');
        btnLoading.classList.remove('hidden');
        sendBtn.disabled = true;

        // If this is just for demo, prevent actual submission
        // Remove these lines when you have actual backend
        // e.preventDefault();
        // setTimeout(() => {
        //     btnText.classList.remove('hidden');
        //     btnLoading.classList.add('hidden');
        //     sendBtn.disabled = false;
        // }, 2000);
    });
});
</script>
@endsection
