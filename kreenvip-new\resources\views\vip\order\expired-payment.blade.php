@extends('vip.app')

@section('title', 'Payment Expired - KREEN VIP')

@push('styles')
    <style>
        /* Full width section */
        .full-width-section {
            width: 100vw;
            margin-left: calc(-50vw + 50%);
            padding-left: calc(50vw - 50%);
            padding-right: calc(50vw - 50%);
        }

        /* Header with background image */
        .header-bg {
            background-image: url('https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');
            background-size: cover;
            background-position: center;
            position: relative;
        }

        .header-bg::before {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5));
            z-index: 1;
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        /* Remove any bottom margins/padding */
        .full-width-section:last-child {
            margin-bottom: 0 !important;
            padding-bottom: 0 !important;
        }

        body {
            margin-bottom: 0 !important;
        }

        /* Expired payment styles */
        .expired-container {
            min-height: 60vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .warning-icon {
            width: 80px;
            height: 80px;
            /* background: linear-gradient(135deg, #fbbf24, #f59e0b); */
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            margin: 0 auto 2rem;
        }

        .order-again-btn {
            background: linear-gradient(135deg, #fbbf24, #f59e0b);
            color: #000;
            font-weight: 600;
            padding: 12px 32px;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
        }

        .order-again-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(251, 191, 36, 0.3);
        }

        /* Progress indicator responsive design */
        @media (max-width: 768px) {
            .booking-progress {
                display: none !important;
            }
            /* Show mobile version */
            .booking-progress-mobile {
                display: flex !important;
                justify-content: center;
                padding: 0.5rem 0;
                border-top: 1px solid #374151;
                margin-top: 1rem;
            }

            .warning-icon {
                width: 60px;
                height: 60px;
            }

            .expired-title {
                font-size: 1.875rem !important;
            }
        }

        @media (min-width: 769px) {
            .booking-progress-mobile {
                display: none !important;
            }
        }
    </style>
@endpush

@section('content')
    <!-- Full Width Header with Background Image -->
    <section class="full-width-section header-bg py-16">
        <div class="header-content text-center">
            <h1 class="text-4xl md:text-6xl font-bold text-white mb-4">{{ $venue->nama ?? "HELEN'S NIGHT MART" }}</h1>
            <p class="text-gray-300 text-lg">{{ $venue->alamat ?? "Jl. Gading Serpong Boulevard No. 3 Bea Plaza, Curug Banten, Tangerang 15810 Indonesia" }}</p>
        </div>
    </section>

    <!-- Full Width Expired Payment Section -->
    <section class="full-width-section py-16 bg-gradient-to-b from-black to-gray-900">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="expired-container">
                <div class="text-center max-w-md mx-auto">
                    <!-- Warning Icon -->
                    <div class="warning-icon">
                        <img src="{{ asset('image/data/expired.png') }}" alt="Warning">
                    </div>

                    <h1 class="expired-title text-2xl md:text-4xl font-bold text-white mb-6 whitespace-nowrap text-center">
                        Your Payment Has Expired
                    </h1>

                    <!-- Description -->
                    <div class="mb-8 space-y-2">
                        <p class="text-gray-300 text-lg">The payment time for your order has expired.</p>
                        <p class="text-gray-300 text-lg">No worries — you can easily place your order again.</p>
                    </div>

                    <!-- Order Again Button -->
                    <button class="order-again-btn mb-6" onclick="orderAgain()">
                        Order Again
                    </button>

                    <!-- Help Text -->
                    <p class="text-gray-500 text-sm">
                        Need help? Contact our support team for assistance.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Full Width Bottom Section - No bottom padding -->
    <section class="full-width-section bg-gray-900" style="padding-top: 1rem; padding-bottom: 10rem;">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Empty space for consistency -->
        </div>
    </section>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Replace header navigation with progress indicator
            replaceHeaderNavigation();
            
            // Add mobile progress indicator
            addMobileProgress();
        });

        // Header Navigation Replacement for Booking Flow
        function replaceHeaderNavigation() {
            // Hide existing navigation
            const desktopNav = document.querySelector('nav.hidden.md\\:flex');
            const loginButton = document.querySelector('a[href=""].hidden.md\\:inline-block');
            
            if (desktopNav) {
                desktopNav.style.display = 'none';
            }
            if (loginButton) {
                loginButton.style.display = 'none';
            }

            // Create progress indicator
            const headerContainer = document.querySelector('header .flex.items-center.justify-between');
            if (headerContainer && !document.querySelector('.booking-progress')) {
                const progressContainer = document.createElement('div');
                progressContainer.className = 'booking-progress flex items-center space-x-8';
                progressContainer.innerHTML = `
                    <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                            <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <span class="text-green-500 font-medium text-sm">Guest Info</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-8 h-0.5 bg-gray-600"></div>
                        <svg class="w-3 h-3 text-gray-600 mx-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                            <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <span class="text-red-500 font-medium text-sm">Payment</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-8 h-0.5 bg-gray-600"></div>
                        <svg class="w-3 h-3 text-gray-600 mx-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 border-2 border-gray-600 rounded-full flex items-center justify-center">
                            <span class="text-gray-600 text-sm font-bold">3</span>
                        </div>
                        <span class="text-gray-600 font-medium text-sm">You're In!</span>
                    </div>
                `;

                // Insert progress indicator after logo
                const logoContainer = headerContainer.querySelector('.flex.items-center');
                if (logoContainer && logoContainer.nextElementSibling) {
                    headerContainer.insertBefore(progressContainer, logoContainer.nextElementSibling);
                }
            }
        }

        // Initialize header replacement when page loads
        replaceHeaderNavigation();
        // Also handle if header loads after this script
        setTimeout(replaceHeaderNavigation, 100);

        // Add mobile progress indicator
        function addMobileProgress() {
            const mobileMenu = document.querySelector('#mobile-menu');
            if (mobileMenu && !document.querySelector('.booking-progress-mobile')) {
                const mobileProgress = document.createElement('div');
                mobileProgress.className = 'booking-progress-mobile md:hidden flex items-center justify-center space-x-4 px-4 py-3 bg-kreen-dark border-t border-gray-800';
                mobileProgress.innerHTML = `
                    <div class="flex items-center space-x-1">
                        <div class="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                            <svg class="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <span class="text-green-500 font-medium text-[10px] md:text-xs">Info</span>
                    </div>
                    <svg class="w-3 h-3 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                    </svg>
                    <div class="flex items-center space-x-1">
                        <div class="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                            <svg class="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <span class="text-red-500 font-medium text-[10px] md:text-xs">Payment</span>
                    </div>
                    <svg class="w-3 h-3 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                    </svg>
                    <div class="flex items-center space-x-1">
                        <div class="w-5 h-5 border border-gray-600 rounded-full flex items-center justify-center">
                            <span class="text-gray-600 text-xs font-bold">3</span>
                        </div>
                        <span class="text-gray-600 font-medium text-[10px] md:text-xs">In!</span>
                    </div>
                `;
                
                mobileMenu.parentNode.insertBefore(mobileProgress, mobileMenu.nextSibling);
            }
        }

        // Add mobile progress
        addMobileProgress();
        setTimeout(addMobileProgress, 100);

        function orderAgain() {
            // Redirect back to booking page or show confirmation
            if (confirm('Redirect to booking page to place your order again?')) {
                // Replace with your actual booking URL
                window.location.href = '/booking';
            }
        }

        // Auto redirect after certain time (optional)
        setTimeout(function() {
            const autoRedirect = confirm('Would you like to automatically return to the booking page?');
            if (autoRedirect) {
                window.location.href = '/booking';
            }
        }, 30000); // 30 seconds
    </script>
@endpush