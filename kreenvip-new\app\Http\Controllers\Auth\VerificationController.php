<?php

namespace App\Http\Controllers\Auth;

use App\Models\User;
use App\Helper\ApiHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Mail\SendVerificationEmail;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;

class VerificationController extends Controller
{

    public function sendEmail(Request $request)
    {
        try {
            $email = Auth::user()->email;
            $user = User::where('email', $email)->first();
            if (!$user) {
                return redirect()->back()->with('error', __("User tidak ditemukan"));
            }
            $enc_email = encrypt($email);
            $timestamp = time();

            $queryData = [
                'd' => $enc_email,
                'ts' => $timestamp,
            ];
            $url = route('auth.verificationEmail.verify', $queryData);
            Mail::to($email)->send(new SendVerificationEmail(['url' => $url]));
            return redirect()->back()->with('success', __("Email berhasil dikirim"));
        } catch (\Exception $e) {
            return redirect()->back()->with('success', __("Email gagal dikirim"));
        }

    }

    public function verify(Request $request)
    {
        $request->validate([
            'd' => 'required',
            'ts' => 'required',
        ]);
        $d = $request->d;
        $timestamp = $request->ts;
        if (time() - $timestamp > 1800) {
            abort(404);
        }
        $email = decrypt($d);
        $user = User::where('email', $email)->first();
        if (!$user) {
            abort(404);
        }
        User::where('email', $email)->update([
            'email_verified_at' => date('Y-m-d H:i:s'),
            'verified_email' => '1',
        ]);

        return redirect()->route('auth.verificationEmail.success');
    }

    public function success()
    {
        return view('auth.verification-email.success');
    }

}
