@extends('vip.app')
@section('title', 'Meet The DJs - KREEN VIP')

@push('styles')
<style>
/* DJ Grid Styles */
.dj-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

@media (min-width: 768px) {
    .dj-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .dj-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* DJ Card Styles */
.dj-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid rgba(212, 175, 55, 0.2);
}

.dj-card:hover {
    transform: translateY(-8px);
    border-color: rgba(212, 175, 55, 0.6);
    box-shadow: 0 20px 40px rgba(212, 175, 55, 0.1);
}

.dj-image {
    width: 100%;
    height: 300px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
}

.dj-image::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.3) 100%);
}

.dj-info {
    padding: 1.5rem;
    text-align: center;
}

.dj-name {
    color: white;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.dj-genre {
    color: #D4AF37;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.dj-origin {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
}

/* Filter Dropdowns */
.filter-container {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.filter-dropdown {
    position: relative;
    min-width: 150px;
}

.filter-select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.75rem 2.5rem 0.75rem 1rem;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 500;
    appearance: none;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.filter-select:focus {
    outline: none;
    border-color: #D4AF37;
    background: rgba(255, 255, 255, 0.15);
}

.filter-select option {
    background: #1a1a1a;
    color: white;
}

.filter-icon {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    width: 1.25rem;
    height: 1.25rem;
    color: #D4AF37;
    pointer-events: none;
}

/* Load More Button */
.load-more-btn {
    background: linear-gradient(135deg, #D4AF37, #B8941F);
    color: black;
    font-weight: 600;
    padding: 0.875rem 2rem;
    border-radius: 0.5rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.load-more-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(212, 175, 55, 0.3);
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .filter-container {
        gap: 1rem;
    }
    
    .filter-dropdown {
        min-width: 120px;
    }
    
    .dj-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    .dj-image {
        height: 200px;
    }
    
    .dj-info {
        padding: 1rem;
    }
}
</style>
@endpush

@section('content')
<!-- Meet The DJs Page Background Container -->
<div class="explore-nights-bg-container2">
    <!-- Main Content -->
    <section class="explore-nights-content py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Page Header -->
            <div class="text-center mb-12">
                <h1 class="text-4xl md:text-5xl font-bold text-white mb-8">Meet The DJs</h1>
                
                <!-- Filter Dropdowns -->
                <div class="filter-container">
                    <!-- Origin Filter -->
                    <div class="filter-dropdown">
                        <select id="origin-filter" class="filter-select">
                            <option value="">Origin</option>
                            <option value="jakarta">Jakarta</option>
                            <option value="bandung">Bandung</option>
                            <option value="surabaya">Surabaya</option>
                            <option value="bali">Bali</option>
                            <option value="international">International</option>
                        </select>
                        <svg class="filter-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    
                    <!-- Gender Filter -->
                    <div class="filter-dropdown">
                        <select id="gender-filter" class="filter-select">
                            <option value="">Gender</option>
                            <option value="male">Male</option>
                            <option value="female">Female</option>
                        </select>
                        <svg class="filter-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- DJs Grid -->
            <div class="dj-grid" id="dj-grid">
                <!-- DJ 1 -->
                <div class="dj-card" data-origin="jakarta" data-gender="male">
                    <div class="dj-image" style="background-image: url('https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');"></div>
                    <div class="dj-info">
                        <h3 class="dj-name">Winky Wiryawan</h3>
                        <!-- <div class="dj-origin">
                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                            </svg>
                            Jakarta
                        </div> -->
                    </div>
                </div>

                <!-- DJ 2 -->
                <div class="dj-card" data-origin="bandung" data-gender="female">
                    <div class="dj-image" style="background-image: url('https://images.unsplash.com/photo-1494790108755-2616c9c0e8e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');"></div>
                    <div class="dj-info">
                        <h3 class="dj-name">Putri Una</h3>
                        <!-- <div class="dj-origin">
                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                            </svg>
                            Bandung
                        </div> -->
                    </div>
                </div>

                <!-- DJ 3 -->
                <div class="dj-card" data-origin="jakarta" data-gender="male">
                    <div class="dj-image" style="background-image: url('https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');"></div>
                    <div class="dj-info">
                        <h3 class="dj-name">Djoha Barus</h3>
                        <!-- <div class="dj-origin">
                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                            </svg>
                            Jakarta
                        </div> -->
                    </div>
                </div>

                <!-- DJ 4 -->
                <div class="dj-card" data-origin="bali" data-gender="female">
                    <div class="dj-image" style="background-image: url('https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');"></div>
                    <div class="dj-info">
                        <h3 class="dj-name">DJ Yasmin</h3>
                        <!-- <div class="dj-origin">
                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                            </svg>
                            Bali
                        </div> -->
                    </div>
                </div>

                <!-- DJ 5 -->
                <div class="dj-card" data-origin="surabaya" data-gender="male">
                    <div class="dj-image" style="background-image: url('https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');"></div>
                    <div class="dj-info">
                        <h3 class="dj-name">Alex Soundwave</h3>
                        <!-- <div class="dj-origin">
                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                            </svg>
                            Surabaya
                        </div> -->
                    </div>
                </div>

                <!-- DJ 6 -->
                <div class="dj-card" data-origin="jakarta" data-gender="female">
                    <div class="dj-image" style="background-image: url('https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');"></div>
                    <div class="dj-info">
                        <h3 class="dj-name">Maya Beats</h3>
                        <!-- <div class="dj-origin">
                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                            </svg>
                            Jakarta
                        </div> -->
                    </div>
                </div>

                <!-- DJ 7 -->
                <div class="dj-card" data-origin="bandung" data-gender="male">
                    <div class="dj-image" style="background-image: url('https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');"></div>
                    <div class="dj-info">
                        <h3 class="dj-name">Rio Electronica</h3>
                        <!-- <div class="dj-origin">
                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                            </svg>
                            Bandung
                        </div> -->
                    </div>
                </div>

                <!-- DJ 8 -->
                <div class="dj-card" data-origin="bali" data-gender="female">
                    <div class="dj-image" style="background-image: url('https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');"></div>
                    <div class="dj-info">
                        <h3 class="dj-name">Luna Vibes</h3>
                        <!-- <div class="dj-origin">
                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                            </svg>
                            Bali
                        </div> -->
                    </div>
                </div>

                <!-- DJ 9 -->
                <div class="dj-card" data-origin="international" data-gender="male">
                    <div class="dj-image" style="background-image: url('https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');"></div>
                    <div class="dj-info">
                        <h3 class="dj-name">Marcus Groove</h3>
                        <!-- <div class="dj-origin">
                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                            </svg>
                            Amsterdam
                        </div> -->
                    </div>
                </div>

                <!-- DJ 10 -->
                <div class="dj-card" data-origin="international" data-gender="female">
                    <div class="dj-image" style="background-image: url('https://images.unsplash.com/photo-1494790108755-2616c9c0e8e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');"></div>
                    <div class="dj-info">
                        <h3 class="dj-name">Sophia Pulse</h3>
                        <div class="dj-origin">
                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                            </svg>
                            Berlin
                        </div>
                    </div>
                </div>

                <!-- DJ 11 -->
                <div class="dj-card" data-origin="jakarta" data-gender="male">
                    <div class="dj-image" style="background-image: url('https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');"></div>
                    <div class="dj-info">
                        <h3 class="dj-name">Kevin Rhythm</h3>
                        <div class="dj-origin">
                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                            </svg>
                            Jakarta
                        </div>
                    </div>
                </div>

                <!-- DJ 12 -->
                <div class="dj-card" data-origin="surabaya" data-gender="female">
                    <div class="dj-image" style="background-image: url('https://images.unsplash.com/photo-1534528741775-53994a69daeb?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');"></div>
                    <div class="dj-info">
                        <h3 class="dj-name">Dina Harmony</h3>
                        <div class="dj-origin">
                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                            </svg>
                            Surabaya
                        </div>
                    </div>
                </div>
            </div>

            <!-- Load More Button -->
            <div class="text-center mt-12">
                <button class="load-more-btn" id="load-more-btn">
                    Load More DJs
                </button>
            </div>
        </div>
    </section>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function () {
    const djCards = document.querySelectorAll('.dj-card');
    const originFilter = document.getElementById('origin-filter');
    const genderFilter = document.getElementById('gender-filter');
    const loadMoreBtn = document.getElementById('load-more-btn');

    // DJ card click handlers
    djCards.forEach(card => {
        card.addEventListener('click', function() {
            const djName = this.querySelector('.dj-name').textContent;
            console.log('DJ clicked:', djName);
            // Add navigation to DJ detail page here
            // window.location.href = `/dj/${djName.toLowerCase().replace(/\s+/g, '-')}`;
        });
    });

    // Filter functionality
    function filterDJs() {
        const selectedOrigin = originFilter.value.toLowerCase();
        const selectedGender = genderFilter.value.toLowerCase();

        djCards.forEach(card => {
            const djOrigin = card.getAttribute('data-origin').toLowerCase();
            const djGender = card.getAttribute('data-gender').toLowerCase();

            const originMatch = !selectedOrigin || djOrigin === selectedOrigin;
            const genderMatch = !selectedGender || djGender === selectedGender;

            if (originMatch && genderMatch) {
                card.style.display = 'block';
                // Add fade in animation
                card.style.opacity = '0';
                setTimeout(() => {
                    card.style.opacity = '1';
                }, 100);
            } else {
                card.style.display = 'none';
            }
        });

        // Update load more button visibility
        const visibleCards = Array.from(djCards).filter(card => card.style.display !== 'none');
        if (visibleCards.length === 0) {
            loadMoreBtn.style.display = 'none';
        } else {
            loadMoreBtn.style.display = 'inline-block';
        }
    }

    // Filter event listeners
    originFilter.addEventListener('change', filterDJs);
    genderFilter.addEventListener('change', filterDJs);

    // Load more functionality
    loadMoreBtn.addEventListener('click', function() {
        console.log('Load more DJs clicked');
        // Add load more functionality here
        // This could load more DJs via AJAX or show hidden cards
        
        // Example: Show loading state
        this.textContent = 'Loading...';
        this.disabled = true;
        
        // Simulate loading delay
        setTimeout(() => {
            this.textContent = 'Load More DJs';
            this.disabled = false;
            // Add new DJ cards here
        }, 1000);
    });

    // Smooth scroll effect for navbar
    const navbar = document.querySelector('.navbar, header, nav');
    if (navbar) {
        window.addEventListener('scroll', function() {
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
    }

    // Add entrance animation for DJ cards
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Initially hide cards for animation
    djCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        card.style.transitionDelay = `${index * 0.1}s`;
        observer.observe(card);
    });
});
</script>
@endpush
