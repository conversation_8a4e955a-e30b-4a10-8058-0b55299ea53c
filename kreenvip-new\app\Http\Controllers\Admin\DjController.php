<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;

class DjController extends Controller
{
    public function index()
    {

        $user = Auth::user();
        $merchantId = $user->id_merchant ?? null;

        if (!$merchantId) {
           abort(404);
        } 

        return view('admin.djs');
    }

    public function getDjs(Request $request)
    {
        $limit = $request->input('limit', 10);
        $page = $request->input('page', 1);
        $offset = ($page - 1) * $limit;
        $search = $request->input('search');
        $status = $request->input('status');

        // Base query - DJ adalah data master untuk semua merchant
        $baseQuery = DB::table('tbl_bar_dj');

        // Filter by status if provided
        if ($status !== null && $status !== '') {
            $baseQuery->where('flag_aktif', $status);
        }

        if ($search) {
            $baseQuery->where(function($query) use ($search) {
                $query->where('nama', 'like', "%{$search}%")
                      ->orWhere('origin', 'like', "%{$search}%")
                      ->orWhere('deskripsi', 'like', "%{$search}%");
            });
        }

        // Clone for count
        $count_all = (clone $baseQuery)->count();
        $has_more = $count_all > $page * $limit;

        // Get paginated data
        $djs = (clone $baseQuery)
            ->select('id', 'nama', 'origin', 'gender', 'gambar', 'slug', 'flag_aktif', 'created_at')
            ->orderBy('created_at', 'desc')
            ->offset($offset)
            ->limit($limit)
            ->get();

        $last_page = ceil($count_all / $limit);

        return response()->json([
            'success' => true,
            'djs' => $djs,
            'pagination' => [
                'total_page' => $last_page,
                'total_item' => $count_all,
                'page' => $page,
                'limit' => $limit,
                'has_more' => $has_more,
                'showing_from' => $offset + 1,
                'showing_to' => min($offset + $limit, $count_all),
                'total_count' => $count_all
            ]
        ]);
    }

    public function store(Request $request)
    {
        try {
            $request->validate([
                'nama' => 'required|string|max:255',
                'deskripsi' => 'nullable|string',
                'origin' => 'nullable|string|max:255',
                'gender' => 'required|in:Male,Female,Other',
                'alamat' => 'nullable|string|max:500',
                'spotify' => 'nullable|url|max:255',
                'fb' => 'nullable|url|max:255',
                'yt_music' => 'nullable|url|max:255',
                'yt' => 'nullable|url|max:255',
                'soundcloud' => 'nullable|url|max:255',
                'ig' => 'nullable|string|max:100',
                'flag_aktif' => 'required|in:0,1',
                'gambar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                'galleries.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
            ]);

            $slug = Str::slug($request->nama . '-' . rand(0, 9999));

            // Handle main image upload
            $imagePath = null;
            if ($request->hasFile('gambar')) {
                $image = $request->file('gambar');
                $imageName = time() . '_dj_' . $image->getClientOriginalName();
                $image->move(public_path('uploads/djs'), $imageName);
                $imagePath = '/uploads/djs/' . $imageName;
            }

            // Insert DJ
            $djId = (int) substr(str_replace('.', '', microtime(true)), 0, 10);

            DB::table('tbl_bar_dj')->insert([
                'id' => $djId,
                'nama' => $request->nama,
                'deskripsi' => $request->deskripsi,
                'origin' => $request->origin,
                'gender' => $request->gender,
                'alamat' => $request->alamat,
                'spotify' => $request->spotify,
                'fb' => $request->fb,
                'yt_music' => $request->yt_music,
                'yt' => $request->yt,
                'soundcloud' => $request->soundcloud,
                'ig' => $request->ig,
                'gambar' => $imagePath,
                'slug' => $slug,
                'flag_aktif' => $request->flag_aktif,
                'created_at' => now(),
                'updated_at' => now()
            ]);

            // Handle gallery uploads
            if ($request->hasFile('galleries')) {
                foreach ($request->file('galleries') as $index => $gallery) {
                    $galleryName = time() . '_gallery_' . $index . '_' . $gallery->getClientOriginalName();
                    $gallery->move(public_path('uploads/djs/galleries'), $galleryName);

                    DB::table('tbl_bar_dj_galeri')->insert([
                        'id' => (int) substr(str_replace('.', '', microtime(true)), 0, 10) + $index,
                        'id_dj' => $djId,
                        'gambar' => '/uploads/djs/galleries/' . $galleryName,
                        'flag_aktif' => '1',
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'DJ berhasil ditambahkan!'
            ]);

        } catch (\Exception $e) {
            Log::error('Error creating DJ: ' . $e->getMessage());
            
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()->with('error', 'Error: ' . $e->getMessage());
        }
    }

    public function show($id)
    {
        try {
            $dj = DB::table('tbl_bar_dj')
                ->where('id', $id)
                ->first();

            if (!$dj) {
                return response()->json([
                    'success' => false,
                    'message' => 'DJ tidak ditemukan.'
                ], 404);
            }

            // Get galleries
            $galleries = DB::table('tbl_bar_dj_galeri')
                ->where('id_dj', $id)
                ->where('flag_aktif', '1')
                ->get();

            return response()->json([
                'success' => true,
                'dj' => $dj,
                'galleries' => $galleries
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ], 500);
        }
    }

    public function update(Request $request, $id)
    {
        try {
            // Check if DJ exists
            $dj = DB::table('tbl_bar_dj')
                ->where('id', $id)
                ->first();

            if (!$dj) {
                return response()->json([
                    'success' => false,
                    'message' => 'DJ tidak ditemukan.'
                ], 404);
            }

            $request->validate([
                'nama' => 'required|string|max:255',
                'deskripsi' => 'nullable|string',
                'origin' => 'nullable|string|max:255',
                'gender' => 'required|in:Male,Female,Other',
                'alamat' => 'nullable|string|max:500',
                'spotify' => 'nullable|url|max:255',
                'fb' => 'nullable|url|max:255',
                'yt_music' => 'nullable|url|max:255',
                'yt' => 'nullable|url|max:255',
                'soundcloud' => 'nullable|url|max:255',
                'ig' => 'nullable|string|max:100',
                'flag_aktif' => 'required|in:0,1',
                'gambar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                'galleries.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
            ]);

            $slug = $dj->slug;

            // Jika nama berubah → update slug
            if ($request->nama !== $dj->nama) {
                $slug = Str::slug($request->nama . '-' . rand(0, 9999));
            }

            // Handle main image upload
            $imagePath = $dj->gambar; // Keep existing image
            if ($request->hasFile('gambar')) {
                $image = $request->file('gambar');
                $imageName = time() . '_dj_' . $image->getClientOriginalName();
                $image->move(public_path('uploads/djs'), $imageName);
                $imagePath = '/uploads/djs/' . $imageName;
            }

            // Update DJ
            DB::table('tbl_bar_dj')->where('id', $id)->update([
                'nama' => $request->nama,
                'deskripsi' => $request->deskripsi,
                'origin' => $request->origin,
                'gender' => $request->gender,
                'alamat' => $request->alamat,
                'spotify' => $request->spotify,
                'fb' => $request->fb,
                'yt_music' => $request->yt_music,
                'yt' => $request->yt,
                'soundcloud' => $request->soundcloud,
                'ig' => $request->ig,
                'gambar' => $imagePath,
                'slug' => $slug,
                'flag_aktif' => $request->flag_aktif,
                'updated_at' => now()
            ]);

            // Handle gallery uploads
            if ($request->hasFile('galleries')) {
                foreach ($request->file('galleries') as $index => $gallery) {
                    $galleryName = time() . '_gallery_' . $index . '_' . $gallery->getClientOriginalExtension();
                    $gallery->move(public_path('uploads/djs/galleries'), $galleryName);

                    DB::table('tbl_bar_dj_galeri')->insert([
                        'id' => (int) substr(str_replace('.', '', microtime(true)), 0, 10) + $index,
                        'id_dj' => $id,
                        'gambar' => '/uploads/djs/galleries/' . $galleryName,
                        'flag_aktif' => '1',
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'DJ berhasil diupdate!'
            ]);

        } catch (\Exception $e) {
            Log::error('Error updating DJ: ' . $e->getMessage());

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()->with('error', 'Error: ' . $e->getMessage());
        }
    }

    public function destroy($id)
    {
        try {
            // Check if DJ exists
            $dj = DB::table('tbl_bar_dj')
                ->where('id', $id)
                ->first();

            if (!$dj) {
                return response()->json([
                    'success' => false,
                    'message' => 'DJ tidak ditemukan.'
                ], 404);
            }

            // Soft delete DJ
            DB::table('tbl_bar_dj')
                ->where('id', $id)
                ->update([
                    'flag_aktif' => '0',
                    'updated_at' => now()
                ]);

            return response()->json([
                'success' => true,
                'message' => 'DJ berhasil dihapus!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ], 500);
        }
    }

    public function deleteGallery($id)
    {
        try {
            // Check if gallery exists
            $gallery = DB::table('tbl_bar_dj_galeri')
                ->where('id', $id)
                ->first();

            if (!$gallery) {
                return response()->json([
                    'success' => false,
                    'message' => 'Gallery tidak ditemukan.'
                ], 404);
            }

            // Soft delete gallery
            DB::table('tbl_bar_dj_galeri')
                ->where('id', $id)
                ->update([
                    'flag_aktif' => '0',
                    'updated_at' => now()
                ]);

            return response()->json([
                'success' => true,
                'message' => 'Gallery berhasil dihapus!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ], 500);
        }
    }
}
