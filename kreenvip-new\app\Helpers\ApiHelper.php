<?php

namespace App\Helpers;

class ApiHelper
{
    public static function successRes($message = "Success", $data = null, $rc = 200)
    {
        $response = [
            'success' => true,
            'rc' => $rc,
            'message' => $message,
        ];

        // Tambahkan 'data' hanya jika $data tidak null
        if (!is_null($data)) {
            $response['data'] = $data;
        }

        return response()->json($response, $rc);
    }
    public static function errorRes($message = "Error", $data = null, $rc = 500)
    {
        $response = [
            'success' => false,
            'rc' => $rc,
            'message' => $message,
        ];

        // Tambahkan 'data' hanya jika $data tidak null
        if (!is_null($data)) {
            $response['data'] = $data;
        }

        return response()->json($response, $rc);
    }

    public static function successRes2(array $params = [])
    {
        $message = $params['message'] ?? 'Success';
        $data = $params['data'] ?? null;
        $rc = $params['rc'] ?? 200;
        $pagination = $params['pagination'] ?? null;
        $payload = $params['payload'] ?? null;

        $response = [
            'success' => true,
            'rc' => $rc,
            'message' => $message,
        ];

        // Tambahkan 'pagination' hanya jika $pagination tidak null
        if (!is_null($pagination)) {
            $response['pagination'] = $pagination;
        }

        if (!is_null($data)) {
            $response['data'] = $data;
        }

        // Tambahkan 'data' hanya jika $data tidak null
        if (!is_null($payload) && env('APP_DEBUG')) {
            $response['payload'] = $payload;
        }

        return response()->json($response, $rc);
    }
    public static function errorRes2(array $params = [])
    {
        $message = $params['message'] ?? 'Internal Server Error';
        $data = $params['data'] ?? null;
        $rc = $params['rc'] ?? 500;
        $payload = $params['payload'] ?? null;
        $trace = $params['trace'] ?? null;

        $response = [
            'success' => false,
            'message' => $message,
            'rc' => $rc,
        ];


        // Tambahkan 'data' hanya jika $data tidak null
        if (!is_null($data)) {
            $response['data'] = $data;
        }
        // Tambahkan 'data' hanya jika $data tidak null
        if (!is_null($payload) && env('APP_DEBUG')) {
            $response['payload'] = $payload;
        }

        if (!is_null($trace) && env('APP_DEBUG')) {
            $response['trace'] = $trace;
        }

        return response()->json($response, $rc);
    }
}
