@extends('layouts.admin')

@section('title', 'QR Scanner')

@section('content')
<div class="min-h-screen bg-gray-50 py-6">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">QR Scanner</h1>
                    <p class="mt-2 text-gray-600">Scan QR code untuk check-in pengunjung</p>
                </div>
                <a href="{{ route('admin.booking-list') }}" class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"></path>
                    </svg>
                    Kembali ke Booking List
                </a>
            </div>
        </div>

        <!-- Scanner Container -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <div class="p-6">
                <!-- Camera Container -->
                <div class="mb-6">
                    {{-- Mengubah ID kontainer untuk html5-qrcode --}}
                    <div id="qr-video-wrapper" class="relative bg-black rounded-lg overflow-hidden" style="height: 400px;">
                        {{-- html5-qrcode akan membuat elemen <video> di sini --}}
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="w-64 h-64 border-2 border-yellow-400 rounded-lg relative">
                                <div class="absolute top-0 left-0 w-6 h-6 border-t-4 border-l-4 border-yellow-400"></div>
                                <div class="absolute top-0 right-0 w-6 h-6 border-t-4 border-r-4 border-yellow-400"></div>
                                <div class="absolute bottom-0 left-0 w-6 h-6 border-b-4 border-l-4 border-yellow-400"></div>
                                <div class="absolute bottom-0 right-0 w-6 h-6 border-b-4 border-r-4 border-yellow-400"></div>
                            </div>
                        </div>
                        <div id="camera-loading" class="absolute inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center">
                            <div class="text-center text-white">
                                <svg class="animate-spin h-8 w-8 mx-auto mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                <p>Mengakses kamera...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Controls -->
                <div class="flex flex-col sm:flex-row gap-4 items-center justify-center">
                    <button id="start-scan" class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium">
                        <svg class="w-5 h-5 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h3a1 1 0 000 2H6.414l2.293 2.293a1 1 0 11-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010 2h1.586l-2.293 2.293a1 1 0 001.414 1.414L15 8.414V10a1 1 0 102 0V6a1 1 0 00-1-1h-3z" clip-rule="evenodd"></path>
                            <path fill-rule="evenodd" d="M5 12a1 1 0 011 1v1.586l2.293-2.293a1 1 0 011.414 1.414L7.414 16H9a1 1 0 110 2H6a1 1 0 01-1-1v-3a1 1 0 011-1zm10 0a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 110-2h1.586l-2.293-2.293a1 1 0 011.414-1.414L15 13.586V12a1 1 0 011-1z" clip-rule="evenodd"></path>
                        </svg>
                        Mulai Scan
                    </button>
                    <button id="stop-scan" class="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-medium hidden">
                        <svg class="w-5 h-5 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z" clip-rule="evenodd"></path>
                        </svg>
                        Stop Scan
                    </button>
                </div>

                <!-- Manual Input Fallback -->
                <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">Input Manual QR Code</h4>
                    <div class="flex gap-3">
                        <input type="text" id="manual-qr-input" placeholder="Masukkan ID Order..." class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <button id="manual-scan-btn" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            Scan Manual
                        </button>
                    </div>
                    <p class="text-xs text-gray-500 mt-2">Gunakan ini jika kamera tidak berfungsi</p>
                </div>

                <!-- Status -->
                <div id="scan-status" class="mt-4 text-center text-gray-600">
                    Klik "Mulai Scan" untuk memulai scanning QR code
                </div>
            </div>
        </div>

        <!-- Recent Scans -->
        <div class="mt-8 bg-white rounded-xl shadow-sm border border-gray-100">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Scan Terakhir</h3>
                <div id="recent-scans" class="space-y-3">
                    <p class="text-gray-500 text-center py-4">Belum ada scan yang dilakukan</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success Modal -->
<div id="success-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 m-4 max-w-md w-full">
        <div class="text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Scan Berhasil!</h3>
            <div id="success-content" class="text-sm text-gray-600 mb-4">
                <!-- Content will be populated by JavaScript -->
            </div>
            <button id="close-success-modal" class="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                OK
            </button>
        </div>
    </div>
</div>

<!-- Error Modal -->
<div id="error-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 m-4 max-w-md w-full">
        <div class="text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Scan Gagal</h3>
            <div id="error-content" class="text-sm text-gray-600 mb-4">
                <!-- Content will be populated by JavaScript -->
            </div>
            <button id="close-error-modal" class="w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                OK
            </button>
        </div>
    </div>
</div>

{{-- HANYA MUAT LIBRARY SEKALI DARI CDN --}}
<script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let html5QrCode;
    let isScanning = false;
    const startBtn = document.getElementById('start-scan');
    const stopBtn = document.getElementById('stop-scan');
    const statusDiv = document.getElementById('scan-status');
    const cameraLoading = document.getElementById('camera-loading');
    const successModal = document.getElementById('success-modal');
    const errorModal = document.getElementById('error-modal');
    const recentScans = document.getElementById('recent-scans');
    const manualInput = document.getElementById('manual-qr-input');
    const manualScanBtn = document.getElementById('manual-scan-btn');

    // Check if browser supports camera API
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        statusDiv.textContent = 'Browser Anda tidak mendukung akses kamera. Silakan gunakan browser yang lebih baru atau gunakan input manual.';
        startBtn.disabled = true;
        cameraLoading.classList.add('hidden');
        return;
    }

    // Initialize QR Scanner targeting the wrapper div
    try {
        // Target the new wrapper div ID: "qr-video-wrapper"
        html5QrCode = new Html5Qrcode("qr-video-wrapper");
    } catch (error) {
        console.error('Error initializing QR scanner:', error);
        statusDiv.textContent = 'Gagal menginisialisasi QR scanner. Pastikan elemen kontainer video ada dan ID-nya benar.';
        startBtn.disabled = true;
        cameraLoading.classList.add('hidden');
        return;
    }

    // Add event listener to start button for manual retry
    startBtn.addEventListener('click', startScanning);
    stopBtn.addEventListener('click', stopScanning);

    // Manual scan functionality
    manualScanBtn.addEventListener('click', function() {
        const qrCode = manualInput.value.trim();
        if (qrCode) {
            processQRCode(qrCode);
            manualInput.value = '';
        } else {
            alert('Masukkan ID Order terlebih dahulu');
        }
    });

    manualInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            manualScanBtn.click();
        }
    });

    // Modal close handlers
    document.getElementById('close-success-modal').addEventListener('click', () => {
        successModal.classList.add('hidden');
        successModal.classList.remove('flex');
        // Re-enable scanning after closing modal if it was active
        if (isScanning) {
            startScanning(); // Attempt to restart camera after successful scan
        }
    });

    document.getElementById('close-error-modal').addEventListener('click', () => {
        errorModal.classList.add('hidden');
        errorModal.classList.remove('flex');
        // Re-enable scanning after closing modal if it was active
        if (isScanning) {
            startScanning(); // Attempt to restart camera after error
        }
    });

    async function startScanning() {
        if (isScanning) return;

        statusDiv.textContent = 'Memulai kamera...';
        cameraLoading.classList.remove('hidden');
        startBtn.disabled = true; // Disable button while trying to start

        try {
            const devices = await Html5Qrcode.getCameras();
            if (devices && devices.length > 0) {
                console.log('Available video devices:', devices);
                let cameraId = null;

                // Prioritize back camera (environment) if available
                const environmentCamera = devices.find(device => device.label.toLowerCase().includes('back') || device.label.toLowerCase().includes('environment'));
                if (environmentCamera) {
                    cameraId = environmentCamera.id;
                    console.log('Using environment camera:', environmentCamera.label);
                } else {
                    // Fallback to the first available camera
                    cameraId = devices[0].id;
                    console.log('Using first available camera:', devices[0].label);
                }

                await html5QrCode.start(
                    cameraId,
                    {
                        fps: 10,
                        qrbox: { width: 250, height: 250 },
                        aspectRatio: 1.0,
                        disableFlip: false // Set to true if QR codes are often mirrored
                    },
                    onScanSuccess,
                    onScanFailure
                );

                isScanning = true;
                startBtn.classList.add('hidden');
                stopBtn.classList.remove('hidden');
                statusDiv.textContent = 'Arahkan kamera ke QR code...';
                cameraLoading.classList.add('hidden');
                startBtn.disabled = false; // Re-enable button
            } else {
                throw new Error('Tidak ada kamera yang terdeteksi pada perangkat ini.');
            }
        } catch (err) {
            console.error('Gagal mengakses kamera:', err);
            cameraLoading.classList.add('hidden');
            startBtn.classList.remove('hidden');
            stopBtn.classList.add('hidden');
            startBtn.disabled = false; // Re-enable button

            let errorMessage = 'Gagal mengakses kamera. ';
            if (err.name === 'NotAllowedError') {
                errorMessage += 'Izin kamera ditolak. Harap izinkan akses kamera di pengaturan browser Anda.';
            } else if (err.name === 'NotFoundError') {
                errorMessage += 'Tidak ada kamera yang ditemukan atau terhubung.';
            } else if (err.name === 'NotReadableError') {
                errorMessage += 'Kamera sedang digunakan oleh aplikasi lain atau tidak dapat diakses.';
            } else if (err.name === 'OverconstrainedError') {
                errorMessage += 'Kamera tidak memenuhi persyaratan yang diminta (misalnya, resolusi).';
            } else if (err.name === 'SecurityError') {
                errorMessage += 'Akses kamera diblokir karena masalah keamanan (misalnya, tidak menggunakan HTTPS).';
            } else if (err.message.includes('No cameras found')) {
                errorMessage += 'Tidak ada kamera yang terdeteksi pada perangkat ini.';
            } else {
                errorMessage += 'Terjadi kesalahan tidak dikenal: ' + err.message;
            }
            statusDiv.textContent = errorMessage + ' Silakan gunakan input manual di bawah.';
        }
    }

    function stopScanning() {
        if (!isScanning) return;

        html5QrCode.stop().then(() => {
            isScanning = false;
            startBtn.classList.remove('hidden');
            stopBtn.classList.add('hidden');
            statusDiv.textContent = 'Scanner dihentikan. Klik "Mulai Scan" untuk memulai lagi.';
        }).catch(err => {
            console.error('Error stopping scanner:', err);
            statusDiv.textContent = 'Gagal menghentikan scanner: ' + err.message;
        });
    }

    function onScanSuccess(decodedText, decodedResult) {
        // Stop scanning temporarily to prevent multiple scans
        stopScanning();
        processQRCode(decodedText);
    }

    function onScanFailure(error) {
        // Handle scan failure silently. This function is called very frequently,
        // so avoid showing modals or heavy UI updates here.
    }

    function processQRCode(qrCode) {
        statusDiv.textContent = 'Memproses QR code...';
        // Send AJAX request to process the scan
        fetch('{{ route("admin.qr-scanner.scan") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                qr_code: qrCode
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccessModal(data);
                addToRecentScans(data.booking, true);
            } else {
                showErrorModal(data.message, data.booking);
                if (data.booking) {
                    addToRecentScans(data.booking, false);
                }
            }
            statusDiv.textContent = 'Klik "Mulai Scan" untuk scan QR code berikutnya';
        })
        .catch(error => {
            console.error('Error:', error);
            showErrorModal('Terjadi kesalahan saat memproses QR code: ' + error.message);
            statusDiv.textContent = 'Klik "Mulai Scan" untuk scan QR code berikutnya';
        });
    }

    function showSuccessModal(data) {
        const content = document.getElementById('success-content');
        content.innerHTML = `
            <div class="text-left space-y-2">
                <p><strong>Nama:</strong> ${data.booking.nama_pengunjung}</p>
                <p><strong>Venue:</strong> ${data.booking.venue_name}</p>
                <p><strong>Tanggal:</strong> ${data.booking.tanggal_kunjungan}</p>
                <p><strong>WhatsApp:</strong> ${data.booking.nomor_whatsapp}</p>
                <p><strong>Total:</strong> ${data.booking.total_amount}</p>
            </div>
        `;
        successModal.classList.remove('hidden');
        successModal.classList.add('flex');
    }

    function showErrorModal(message, booking = null) {
        const content = document.getElementById('error-content');
        let html = `<p>${message}</p>`;

        if (booking) {
            html += `
                <div class="text-left space-y-2 mt-3 pt-3 border-t">
                    <p><strong>Nama:</strong> ${booking.nama_pengunjung}</p>
                    <p><strong>Venue:</strong> ${booking.venue_name}</p>
                    <p><strong>Tanggal:</strong> ${booking.tanggal_kunjungan}</p>
                    <p><strong>Status:</strong> ${booking.status}</p>
                </div>
            `;
        }
        content.innerHTML = html;
        errorModal.classList.remove('hidden');
        errorModal.classList.add('flex');
    }

    function addToRecentScans(booking, success) {
        const scanItem = document.createElement('div');
        scanItem.className = `p-3 rounded-lg border ${success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`;

        const now = new Date().toLocaleTimeString('id-ID');
        const statusColor = success ? 'text-green-600' : 'text-red-600';
        const statusText = success ? 'Berhasil' : 'Gagal';

        scanItem.innerHTML = `
            <div class="flex justify-between items-start">
                <div>
                    <p class="font-medium">${booking.nama_pengunjung}</p>
                    <p class="text-sm text-gray-600">${booking.venue_name}</p>
                    <p class="text-sm text-gray-600">${booking.tanggal_kunjungan}</p>
                </div>
                <div class="text-right">
                    <p class="text-sm ${statusColor} font-medium">${statusText}</p>
                    <p class="text-xs text-gray-500">${now}</p>
                </div>
            </div>
        `;

        // Remove "no scans" message if it exists
        const noScansMsg = recentScans.querySelector('p.text-gray-500');
        if (noScansMsg) {
            noScansMsg.remove();
        }

        // Add to top of recent scans
        recentScans.insertBefore(scanItem, recentScans.firstChild);

        // Keep only last 5 scans
        while (recentScans.children.length > 5) {
            recentScans.removeChild(recentScans.lastChild);
        }
    }

    // Initial camera check and start attempt on page load
    // This will try to start the camera automatically when the page loads.
    // If it fails, the "Mulai Scan" button will be enabled for manual retry.
    async function checkAndStartCameraOnLoad() {
        // Check if browser supports camera API
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            statusDiv.textContent = 'Browser Anda tidak mendukung akses kamera. Silakan gunakan browser yang lebih baru atau gunakan input manual.';
            startBtn.disabled = true;
            cameraLoading.classList.add('hidden');
            return;
        }

        try {
            // Attempt to start scanning immediately
            await startScanning();
        } catch (err) {
            console.error('Initial camera start failed:', err);
            // Error message will be set by startScanning's catch block
        }
    }

    // Call the function on page load
    checkAndStartCameraOnLoad();
});
</script>
@endsection