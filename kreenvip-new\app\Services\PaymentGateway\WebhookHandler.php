<?php
namespace App\Services\PaymentGateway;

use Exception;
use App\Helpers\Email;
use Illuminate\Support\Str;
use App\Mail\SendForgotPassword;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class WebhookHandler
{
    protected $transaction_max_attempt;
    protected $transaction_delay;
    public function __construct()
    {
        $this->transaction_max_attempt = config('app.transaction_max_attempt') ?? 5;
        $this->transaction_delay = config('app.transaction_delay') ?? 1000;
    }
    public function handler($pgTable, $typeOrder = 'bar', $idOrder, $statusPg)
    {
        // Get Order
        $order = DB::select("SELECT * FROM tbl_{$typeOrder}_order o WHERE o.id = ? LIMIT 1", [$idOrder]);
        if (empty($order)) {
            return [
                'success' => false,
                'message' => 'Order not found',
            ];
        }

        if ($order[0]->status == 1) {
            return [
                'success' => true,
                'message' => 'Order sudah selesai',
            ];
        } else {
            $statusPg = Str::lower($statusPg);
            $idProduct = $order[0]->{"id_venue"};
            if ($statusPg === "paid" || $statusPg === "succeeded") {
                $statuses = $this->handleSuccess($typeOrder, $idProduct, $idOrder);
                if (!$statuses['success']) {
                    return $statuses;
                }
            } else if ($statusPg === "failed") {
                $statuses = $this->handleFailed($typeOrder, $idProduct, $idOrder);
                if (!$statuses['success']) {
                    return $statuses;
                }
            } else if ($statusPg === "expired") {
                $statuses = $this->handleExpired($typeOrder, $idProduct, $idOrder);
                if (!$statuses['success']) {
                    return $statuses;
                }
            } else {
                return [
                    'success' => false,
                    'message' => 'Event payment tidak dikenali',
                ];
            }
            $statusOrder = $statuses['statusOrder'];
            $statusPayment = $statuses['statusPayment'];
        }

        // Update Order
        $attempt = 1;
        while ($attempt <= $this->transaction_max_attempt) {
            try {
                DB::beginTransaction();
                $orderDetails = DB::table("tbl_{$typeOrder}_order_detail")
                    ->where('id_order', $idOrder)
                    ->get();

                if ($statusOrder == 20) {
                    foreach ($orderDetails as $orderDetail) {
                        DB::table("tbl_{$typeOrder}_jenis_tiket")
                            ->where('id', $orderDetail->id_jenis_tiket)
                            ->update(['stok_tersedia' => DB::raw('stok_tersedia + ' . $orderDetail->qty)]);
                    }
                }
                if ($statusOrder == 1) {
                    foreach ($orderDetails as $orderDetail) {
                        DB::table("tbl_{$typeOrder}_jenis_tiket")
                            ->where('id', $orderDetail->id_jenis_tiket)
                            ->update(['total_terjual' => DB::raw('total_terjual + ' . $orderDetail->qty)]);
                    }
                }
                DB::table($pgTable)
                    ->where('order_id', $idOrder)
                    ->where('status', '!=', '1')
                    ->update(['status' => (string) $statusPayment]);
                DB::table("tbl_{$typeOrder}_order")
                    ->where('id', $idOrder)
                    ->where('status', '!=', '1')
                    ->update(['status' => (string) $statusOrder]);
                DB::commit();
                return [
                    'success' => true,
                ];
            } catch (Exception $e) {
                DB::rollBack();
                $attempt++;
                $message = $e->getMessage();
                $error = [
                    'success' => false,
                    'message' => $message,
                ];
                if (
                    strpos($message, 'Deadlock found') !== false ||
                    strpos($message, 'Lock wait timeout') !== false ||
                    strpos($message, 'SQLSTATE[40001]') !== false ||
                    strpos($message, 'SQLSTATE[HY000]: General error: 1205') !== false
                ) {
                    Log::warning("Retrying transaction attempt $attempt: " . $message);
                    if ($attempt >= $this->transaction_max_attempt) {
                        Log::error("Transaction failed after max attempt: " . $message);
                        return $error;
                    }
                    usleep($this->transaction_delay * 1000); // Delay retry
                } else {
                    Log::info('Error Update Order: ', ['response' => $error]);
                    return $error;
                }
            }
        }
    }
    public function handleSuccess($typeOrder, $idProduct, $idOrder)
    {
        if ($typeOrder === "bar") {
            Email::sendEticketPerEmail($idOrder);
            $statusOrder = 1;
            $statusPayment = 1;
        }

        return [
            'success' => true,
            'statusOrder' => $statusOrder,
            'statusPayment' => $statusPayment
        ];
    }

    public function handleFailed($typeOrder, $idProduct, $idOrder)
    {
        $statusOrder = 0;
        $statusPayment = 0;

        return [
            'success' => true,
            'statusOrder' => $statusOrder,
            'statusPayment' => $statusPayment
        ];
    }

    public function handleExpired($typeOrder, $idProduct, $idOrder)
    {
        $statusOrder = 20;
        $statusPayment = 0;

        return [
            'success' => true,
            'statusOrder' => $statusOrder,
            'statusPayment' => $statusPayment
        ];

    }

}
