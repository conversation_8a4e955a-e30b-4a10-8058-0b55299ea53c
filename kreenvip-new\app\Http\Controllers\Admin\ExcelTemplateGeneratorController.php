<?php
namespace App\Http\Controllers\Admin;

use Illuminate\Support\Facades\DB;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Cell\DataValidation;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use App\Http\Controllers\Controller;

class ExcelTemplateGeneratorController extends Controller
{
    public function generateTemplate()
    {
        try {
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();

            // Define headers
            $headers = [
                'Event Name',
                'Event Date',
                'Event Image URL',
                'Event Description',
                'Event Location',
                'Country Name',
                'Province Name',
                'Regency Name',
                'District Name',
                'Event Active Flag',
                'Venue Name',
                'Merchant ID',
                'Venue Information',
                'Venue Banner URL',
                'Venue Currency',
                'Venue Latitude',
                'Venue Longitude',
                'Venue Address',
                'Venue Active Flag',
                'Venue Instagram Username',
                'Venue WhatsApp Number'
            ];

            foreach ($headers as $index => $header) {
                $columnLetter = Coordinate::stringFromColumnIndex($index + 1);
                $sheet->setCellValue($columnLetter . '1', $header);
            }

            // Fetch data
            $kreenProductionDB = DB::connection('kreen_production_online');

            $countries = $kreenProductionDB->table('tbl_adm_country')->select('name')->orderBy('name')->pluck('name')->toArray();
            $provinces = $kreenProductionDB->table('tbl_adm_province')->select('name')->orderBy('name')->pluck('name')->toArray();
            $regencies = $kreenProductionDB->table('tbl_adm_regency')->select('name')->orderBy('name')->pluck('name')->toArray();
            $districts = $kreenProductionDB->table('tbl_adm_district')->select('name')->orderBy('name')->pluck('name')->toArray();

            // Convert to string list (Excel dropdown list must be comma separated string)
            $countryList  = implode(',', array_map('strval', $countries));
            $provinceList = implode(',', array_map('strval', $provinces));
            $regencyList  = implode(',', array_map('strval', $regencies));
            $districtList = implode(',', array_map('strval', $districts));

            // Helper: Apply dropdown per cell
            $this->applyDropdown($sheet, 'F', $countryList);   // Country Name
            $this->applyDropdown($sheet, 'G', $provinceList);  // Province Name
            $this->applyDropdown($sheet, 'H', $regencyList);   // Regency Name
            $this->applyDropdown($sheet, 'I', $districtList);  // District Name

            // Auto-size columns
            foreach (range('A', 'U') as $columnID) {
                $sheet->getColumnDimension($columnID)->setAutoSize(true);
            }

            // Create writer
            $writer = new Xlsx($spreadsheet);
            $filename = 'event_venue_template_with_dropdown.xlsx';

            // Output
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="' . $filename . '"');
            header('Cache-Control: max-age=0');
            $writer->save('php://output');
            exit;
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error generating template: ' . $e->getMessage()
            ], 500);
        }
    }

    // 🔁 Reusable function for dropdown validation
    private function applyDropdown($sheet, $columnLetter, $itemList)
    {
        foreach (range(2, 1000) as $row) {
            $cell = $columnLetter . $row;
            $validation = $sheet->getCell($cell)->getDataValidation();
            $validation->setType(DataValidation::TYPE_LIST);
            $validation->setErrorStyle(DataValidation::STYLE_STOP);
            $validation->setAllowBlank(true);
            $validation->setShowDropDown(true);
            $validation->setFormula1('"' . $itemList . '"');
            $validation->setShowErrorMessage(true);
            $validation->setErrorTitle('Invalid Input');
            $validation->setError('Please select a value from the dropdown.');
        }
    }
}
?>
