@extends('vip.app')

@section('title', __('Atur Ulang Kata Sandi'))

@section('content')
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gradient-to-br via-gray-900 to-kreen-dark">
    <div class="max-w-md w-full space-y-8">
        <div class="kreen-card kreen-golden-border p-8 backdrop-blur-sm">
            <!-- Logo -->
            <div class="mb-8">
                <div class="flex items-center justify-left mb-4">
                    <div class="w-[120px] h-[60px]">
                        <img src="{{ asset('image/kreenvip/kreenvip.png') }}" alt="KREEN VIP" class="h-12 w-auto object-contain" />
                    </div>
                </div>
                <h2 class="text-xl font-semibold text-white justify-left">
                    {{ __('Atur Ulang Kata Sandi') }}
                </h2>
                <p class="text-sm text-gray-300 justify-left mt-2">
                    {{ __('Silakan masukkan kata sandi baru Anda') }}
                </p>
            </div>

            <!-- Garis Gradient -->
            <div class="mt-1 w-full h-[1px] bg-gradient-to-r from-[#A37A1D] via-[#FFEC95] to-[#281A00]"></div>

            <!-- Form Reset Password -->
            <form method="POST" action="{{ route('auth.forgotPassword.resetProcess') }}" class="space-y-6 mt-6">
                @csrf
                <input type="hidden" name="d" value="{{ $d }}">
                <input type="hidden" name="ts" value="{{ $ts }}">

                <!-- Password Baru -->
                <div>
                    <label for="new_password" class="block text-sm font-medium text-white mb-2">
                        {{ __('Kata Sandi Baru') }}
                    </label>
                    <input
                        type="password"
                        id="new_password"
                        name="new_password"
                        required
                        class="w-full px-4 py-3 bg-white text-black rounded-lg border-0 focus:ring-2 focus:ring-kreen-gold focus:outline-none transition-all @error('new_password') ring-2 ring-red-500 @enderror"
                        placeholder="{{ __('Masukkan Kata Sandi Baru') }}"
                    >
                    @error('new_password')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Konfirmasi Password -->
                <div>
                    <label for="new_password_confirmation" class="block text-sm font-medium text-white mb-2">
                        {{ __('Konfirmasi Kata Sandi Baru') }}
                    </label>
                    <input
                        type="password"
                        id="new_password_confirmation"
                        name="new_password_confirmation"
                        required
                        class="w-full px-4 py-3 bg-white text-black rounded-lg border-0 focus:ring-2 focus:ring-kreen-gold focus:outline-none transition-all @error('new_password_confirmation') ring-2 ring-red-500 @enderror"
                        placeholder="{{ __('Masukkan Konfirmasi Kata Sandi Baru') }}"
                    >
                    @error('new_password_confirmation')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Tombol Submit -->
                <button type="submit"
                        class="w-full bg-kreen-gold hover:bg-kreen-gold-hover text-black font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105">
                    {{ __('Atur Ulang') }}
                </button>
            </form>
        </div>
    </div>
</div>
@endsection
