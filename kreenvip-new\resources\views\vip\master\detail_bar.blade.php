@extends('vip.app')

@section('title', '<PERSON><PERSON><PERSON>\'S NIGHT MART - KREEN VIP')

@push('styles')
<style>
.sticky-card {
    position: sticky;
    top: 10rem;
    z-index: 10;
}

/* Ensure parent containers don't interfere */
.booking-container {
    position: relative;
    overflow: visible;
}

.booking-grid {
    position: relative;
    overflow: visible;
}

/* Mobile sticky adjustments */
@media (max-width: 1024px) {
    .sticky-card {
        position: relative;
        top: auto;
    }
}
</style>
@endpush

@section('content')
<!-- Container dengan Background Image untuk Venue Detail -->
<div class="explore-nights-bg-container">
    <!-- Venue Hero Section -->
    <section class="explore-nights-content py-8">
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Venue Header Image -->
            <div class="mb-8">
                <div class="rounded-xl overflow-hidden h-64 md:h-80" style="background-image: url('https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80'); background-size: cover; background-position: center;">
                </div>
            </div>

            <!-- Welcome Section -->
            <div class="text-center mb-12">
                <p class="text-gray-400 text-lg mb-2">WELCOME TO</p>
                <h1 class="text-4xl md:text-5xl font-bold text-white mb-8">HELEN'S NIGHT MART GADING SERPONG</h1>
            </div>
        </div>
    </section>

    <!-- Booking Section -->
    <section class="explore-nights-content py-12 booking-container">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-12 gap-12 booking-grid">
                <!-- Left Side - Form (8 columns) -->
                <div class="lg:col-span-8 space-y-8">
                    <!-- Choose Date -->
                    <div>
                        <h2 class="text-white text-xl font-semibold mb-4">Choose Date</h2>
                        <div class="relative">
                            <input 
                                type="date" 
                                placeholder="Choose Date"
                                class="w-full bg-transparent border-2 border-kreen-gold text-white px-4 py-4 rounded-lg focus:outline-none focus:ring-2 focus:ring-kreen-gold placeholder-gray-400"
                            >
                        </div>
                    </div>

                    <!-- First Drink Charge Ticket -->
                    <div>
                        <h2 class="text-white text-xl font-semibold mb-4">First Drink Charge Ticket</h2>
                        
                        <div class="border-2 border-kreen-gold rounded-lg p-6 bg-transparent">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-white font-semibold text-lg">FDC Monday & Tuesday</h3>
                                <div class="flex items-center space-x-2">
                                    <button class="w-8 h-8 border border-gray-400 rounded flex items-center justify-center text-white hover:bg-gray-700">
                                        <span>-</span>
                                    </button>
                                    <span class="text-white px-3">1</span>
                                    <button class="w-8 h-8 border border-gray-400 rounded flex items-center justify-center text-white hover:bg-gray-700">
                                        <span>+</span>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <p class="text-gray-300 text-sm mb-2">Inclusion:</p>
                                <p class="text-gray-300 text-sm">• 1 Prost Beer or 1 Juice/Soft Drink</p>
                            </div>
                            
                            <div class="text-right">
                                <p class="text-kreen-gold font-bold text-xl">Rp 150.000</p>
                            </div>
                        </div>
                    </div>

                    <!-- Venue Information Card - All sections in one card -->
                    <div class="border-2 border-kreen-gold rounded-lg p-6 bg-transparent space-y-8">
                        <!-- About Section -->
                        <div>
                            <h2 class="text-white text-xl font-semibold mb-4">About</h2>
                            <h3 class="text-white font-semibold text-lg mb-4">HELEN'S NIGHT MART – Gading Serpong</h3>
                            <p class="text-gray-300 text-sm leading-relaxed mb-4">
                                Experience the vibrant nightlife of Gading Serpong at HELEN'S NIGHT MART, a lively hotspot that combines great music, signature cocktails, and an electrifying crowd. Known for its casual yet stylish atmosphere, Helen's offers the perfect setting to unwind and connect with friends. Whether you're here for the beats, the cocktails, or the vibe — Helen's makes every night feel like a celebration.
                            </p>
                            <p class="text-gray-300 text-sm leading-relaxed">
                                Located strategically at the heart of Gading Serpong, it's the go-to spot for weekend fun and spontaneous weekday getaways.
                            </p>
                        </div>

                        <!-- Divider -->
                        <hr class="border-gray-600">

                        <!-- Open Hours Section -->
                        <div>
                            <h2 class="text-white text-xl font-semibold mb-4">Open Hours</h2>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                        <span class="text-white text-sm">Monday</span>
                                    </div>
                                    <span class="text-gray-300 text-sm">20:00 - 04:00 WIB</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                        <span class="text-white text-sm">Tuesday</span>
                                    </div>
                                    <span class="text-gray-300 text-sm">20:00 - 04:00 WIB</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                        <span class="text-white text-sm">Wednesday</span>
                                    </div>
                                    <span class="text-gray-300 text-sm">20:00 - 04:00 WIB</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                        <span class="text-white text-sm">Thursday</span>
                                    </div>
                                    <span class="text-gray-300 text-sm">20:00 - 04:00 WIB</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                        <span class="text-white text-sm">Friday</span>
                                    </div>
                                    <span class="text-gray-300 text-sm">20:00 - 04:00 WIB</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                        <span class="text-white text-sm">Saturday</span>
                                    </div>
                                    <span class="text-gray-300 text-sm">20:00 - 04:00 WIB</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                        <span class="text-white text-sm">Sunday</span>
                                    </div>
                                    <span class="text-gray-300 text-sm">20:00 - 04:00 WIB</span>
                                </div>
                            </div>
                        </div>

                        <!-- Divider -->
                        <hr class="border-gray-600">

                        <!-- Location Section -->
                        <div>
                            <h2 class="text-white text-xl font-semibold mb-4">Location</h2>
                            <!-- Map Placeholder -->
                            <div class="bg-gray-800 rounded-lg h-48 mb-4 flex items-center justify-center relative overflow-hidden">
                                <div class="absolute inset-0 bg-gradient-to-br from-gray-700 to-gray-900"></div>
                                <div class="relative z-10 text-center">
                                    <svg class="w-12 h-12 text-kreen-gold mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                                    </svg>
                                    <p class="text-gray-400 text-sm">Interactive Map</p>
                                </div>
                                <!-- Map controls -->
                                <div class="absolute top-2 right-2 flex flex-col space-y-1">
                                    <button class="w-8 h-8 bg-white bg-opacity-90 rounded flex items-center justify-center text-gray-800 hover:bg-opacity-100">
                                        <span class="text-lg font-bold">+</span>
                                    </button>
                                    <button class="w-8 h-8 bg-white bg-opacity-90 rounded flex items-center justify-center text-gray-800 hover:bg-opacity-100">
                                        <span class="text-lg font-bold">−</span>
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Address -->
                            <div class="flex items-start space-x-3">
                                <div class="w-5 h-5 flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <svg class="w-4 h-4 text-kreen-gold" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-gray-300 text-sm">Jl. Gading Serpong Boulevard No. 3 Sisa Plaza, Curug Sangereng, Tangerang 15810 Indonesia</p>
                                </div>
                            </div>
                        </div>

                        <!-- Divider -->
                        <hr class="border-gray-600">

                        <!-- Gallery Section -->
                        <div>
                            <h2 class="text-white text-xl font-semibold mb-4">Gallery</h2>
                            <div class="grid grid-cols-3 gap-3">
                                <!-- Gallery Image 1 -->
                                <div class="aspect-square rounded-lg overflow-hidden">
                                    <div class="w-full h-full bg-gradient-to-br from-amber-900 to-amber-700" style="background-image: url('https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'); background-size: cover; background-position: center;"></div>
                                </div>
                                
                                <!-- Gallery Image 2 -->
                                <div class="aspect-square rounded-lg overflow-hidden">
                                    <div class="w-full h-full bg-gradient-to-br from-orange-900 to-red-800" style="background-image: url('https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'); background-size: cover; background-position: center;"></div>
                                </div>
                                
                                <!-- See All Photos Button -->
                                <div class="aspect-square rounded-lg overflow-hidden bg-gray-800 bg-opacity-50 flex items-center justify-center cursor-pointer hover:bg-opacity-70 transition-all">
                                    <div class="text-center">
                                        <svg class="w-8 h-8 text-kreen-gold mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                        </svg>
                                        <p class="text-white text-xs font-medium">See All Photos</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Divider -->
                        <hr class="border-gray-600">

                        <!-- Contact Us Section -->
                        <div>
                            <h2 class="text-white text-xl font-semibold mb-4">Contact Us</h2>
                            <div class="space-y-4">
                                <!-- Email -->
                                <div class="flex items-center space-x-3">
                                    <div class="w-5 h-5 flex items-center justify-center flex-shrink-0">
                                        <svg class="w-4 h-4 text-kreen-gold" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                                            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
                                        </svg>
                                    </div>
                                    <span class="text-gray-300 text-sm">helens@nightmart</span>
                                </div>
                                
                                <!-- Phone -->
                                <div class="flex items-center space-x-3">
                                    <div class="w-5 h-5 flex items-center justify-center flex-shrink-0">
                                        <svg class="w-4 h-4 text-kreen-gold" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                                        </svg>
                                    </div>
                                    <span class="text-gray-300 text-sm">(+62) 812 - 3456 - 7890</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Side - Sticky Summary Card (4 columns) -->
                <div class="lg:col-span-4">
                    <div class="border-2 border-kreen-gold rounded-xl p-6 space-y-6" style="background: linear-gradient(98.07deg, #000000 0%, #2E2E2E 25.48%, #000000 50.48%, #464646 81.73%, #040404 100%); position: sticky; top: 137px;" >
                        <!-- Location -->
                        <div class="flex items-center space-x-3">
                            <div class="w-6 h-6 flex items-center justify-center flex-shrink-0">
                                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <div>
                                <p class="text-white font-medium text-sm">HELEN'S NIGHT MART GADING SERPONG</p>
                            </div>
                        </div>

                        <!-- Date -->
                        <div class="flex items-center space-x-3">
                            <div class="w-6 h-6 flex items-center justify-center flex-shrink-0">
                                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <div>
                                <p class="text-gray-400 text-sm" id="selected-date">Chosen date will be added on here</p>
                            </div>
                        </div>

                        <!-- Ticket - with darker background -->
                        <div class="bg-gray-700 bg-opacity-50 rounded-lg p-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-6 h-6 flex items-center justify-center flex-shrink-0">
                                    <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M2 6a2 2 0 012-2h12a2 2 0 012 2v2a2 2 0 100 4v2a2 2 0 01-2 2H4a2 2 0 01-2-2v-2a2 2 0 100-4V6z"/>
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-gray-400 text-sm" id="selected-ticket">Chosen ticket will be added on here</p>
                                </div>
                            </div>
                        </div>

                        <!-- Divider -->
                        <hr class="border-gray-600">

                        <!-- Total -->
                        <div class="flex justify-between items-center">
                            <span class="text-white font-medium" id="total-text">Total (0 ticket)</span>
                            <span class="text-white font-bold text-lg" id="total-price">Rp 0</span>
                        </div>

                        <!-- Buy Button -->
                        <button class="w-full bg-kreen-gold hover:bg-yellow-500 text-black font-bold py-4 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 text-lg">
                            BUY NOW
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function () {
    // Quantity controls
    const minusBtn = document.querySelector('button:has(span:contains("-"))');
    const plusBtn = document.querySelector('button:has(span:contains("+"))');
    const quantitySpan = document.querySelector('.text-white.px-3');
    let quantity = 1;

    // Plus button
    document.querySelectorAll('button').forEach(btn => {
        if (btn.textContent.includes('+')) {
            btn.addEventListener('click', function() {
                quantity++;
                quantitySpan.textContent = quantity;
                updateTotal();
            });
        }
        if (btn.textContent.includes('-')) {
            btn.addEventListener('click', function() {
                if (quantity > 0) {
                    quantity--;
                    quantitySpan.textContent = quantity;
                    updateTotal();
                }
            });
        }
    });

    // Date input change
    document.querySelector('input[type="date"]').addEventListener('change', function() {
        const selectedDate = new Date(this.value);
        const options = { year: 'numeric', month: 'long', day: 'numeric' };
        const formattedDate = selectedDate.toLocaleDateString('id-ID', options);
        
        // Update the date in summary card
        document.getElementById('selected-date').textContent = formattedDate;
    });

    // Update total function
    function updateTotal() {
        const pricePerTicket = 150000;
        const total = quantity * pricePerTicket;
        const formattedTotal = 'Rp ' + total.toLocaleString('id-ID');
        
        document.getElementById('total-price').textContent = formattedTotal;
        document.getElementById('total-text').textContent = `Total (${quantity} ticket${quantity !== 1 ? 's' : ''})`;
        
        // Update ticket info in summary
        if (quantity > 0) {
            document.getElementById('selected-ticket').textContent = `${quantity}x FDC Monday & Tuesday`;
        } else {
            document.getElementById('selected-ticket').textContent = 'Chosen ticket will be added on here';
        }
    }

    // Buy now button
    document.querySelector('.bg-kreen-gold').addEventListener('click', function() {
        if (quantity > 0) {
            alert('Proceeding to payment...');
            // Add your booking logic here
        } else {
            alert('Please select at least 1 ticket');
        }
    });

    // Enhanced sticky behavior with JavaScript fallback
    function handleStickyCard() {
        const stickyCard = document.querySelector('.sticky-card');
        const bookingContainer = document.querySelector('.booking-container');
        
        if (!stickyCard || !bookingContainer) return;
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    stickyCard.style.position = 'sticky';
                    stickyCard.style.top = '2rem';
                } else {
                    // Fallback for browsers with sticky issues
                    const rect = bookingContainer.getBoundingClientRect();
                    if (rect.top < 0) {
                        stickyCard.style.position = 'fixed';
                        stickyCard.style.top = '2rem';
                        stickyCard.style.width = stickyCard.offsetWidth + 'px';
                    }
                }
            });
        }, {
            threshold: 0.1
        });
        
        observer.observe(bookingContainer);
    }
    
    // Initialize sticky behavior
    handleStickyCard();
});
</script>
@endpush
