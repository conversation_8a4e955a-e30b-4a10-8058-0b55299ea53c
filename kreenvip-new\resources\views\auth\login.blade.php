@extends('vip.app')

@section('title', 'Login - KREEN VIP')

@section('content')
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gradient-to-br via-gray-900 to-kreen-dark">
<!-- <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-kreen-dark via-gray-900 to-kreen-dark"> -->
    <div class="max-w-md w-full space-y-8">
        <div class="kreen-card kreen-golden-border p-8 backdrop-blur-sm">
            <!-- Logo -->
            <div class="mb-8">
                <div class="flex items-center justify-left mb-4">
                    <div class="w-[120px] h-[60px]"> <!-- Ukuran container diperbesar -->
                        <img src="{{ asset('image/kreenvip/kreenvip.png') }}" alt="KREEN VIP" class="h-12 w-auto object-contain" />
                    </div>
                </div>
                <h2 class="text-xl font-semibold text-white">Login KREEN VIP account</h2>
            </div>

            <!-- Gradient line -->
            <div class="mt-1 w-full h-[1px] bg-gradient-to-r from-[#A37A1D] via-[#FFEC95] to-[#281A00]"></div>

            <!-- Login Form -->
            <form method="POST" action="{{ route('auth.submitLogin') }}" class="space-y-6">
                @csrf

                <!-- Email Field -->
                <div>
                    <label for="email" class="block text-sm font-medium text-white mb-2">
                        Email <span class="text-red-500">*</span>
                    </label>
                    <input
                        id="email"
                        name="email"
                        type="email"
                        required
                        class="w-full px-4 py-3 bg-white text-black rounded-lg border-0 focus:ring-2 focus:ring-kreen-gold focus:outline-none transition-all @error('email') ring-2 ring-red-500 @enderror"
                        placeholder="Email"
                        value="{{ old('email') }}"
                    >
                    @error('email')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Password Field -->
                <div>
                    <label for="password" class="block text-sm font-medium text-white mb-2">
                        Password <span class="text-red-500">*</span>
                    </label>
                    <input
                        id="password"
                        name="password"
                        autocomplete="off"
                        type="password"
                        required
                        class="w-full px-4 py-3 bg-white text-black rounded-lg border-0 focus:ring-2 focus:ring-kreen-gold focus:outline-none transition-all @error('password') ring-2 ring-red-500 @enderror"
                        placeholder="Password"
                    >
                    @error('password')
                        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Forgot Password -->
                <div class="text-right">
                    <a href="{{ route('auth.forgotPassword.index') }}" class="text-kreen-gold hover:text-yellow-400 transition-colors text-sm">
                        Forgot password?
                    </a>
                </div>

                <!-- Login Button -->
                <button type="submit" class="w-full bg-kreen-gold hover:bg-kreen-gold-hover text-black font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105">
                    Log in
                </button>

              <!-- Divider -->
                <div class="relative my-6">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full h-[2px] bg-gradient-to-r from-[#A37A1D] via-[#FFF0C0] via-[#FFEC95] to-[#281A00]"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-kreen-card text-gray-400">Log in faster with</span>
                    </div>
                </div>

                <!-- Google Login -->
                <button type="button" onclick="window.location.href = '{{ route('auth.googleRedirect') }}'" class="w-full bg-white hover:bg-gray-100 text-black font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center justify-center gap-2">
                    <svg class="w-5 h-5" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    Log in with Google
                </button>

                <!-- Sign Up Link -->
                <div class="text-center">
                    <span class="text-gray-400">Don't have an account? </span>
                    <a href="{{ route('auth.register') }}" class="text-kreen-gold hover:text-yellow-400 transition-colors font-semibold">Sign Up Now!</a>
                </div>

                <!-- Terms -->
                <div class="text-center text-xs text-gray-400">
                    By logging in, you agree to KREEN's
                    <a href="{{ route('privacy') }}" class="text-kreen-gold hover:text-yellow-400 transition-colors">Privacy Policy</a> and
                    <a href="{{ route('tos') }}" class="text-kreen-gold hover:text-yellow-400 transition-colors">Terms & Conditions</a>.
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
