<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Cek apakah user sudah login
        if (Auth::check()) {
            // Cek apakah role user adalah 'admin'
            if (Auth::user()->role === 'admin') {
                return $next($request);
            }
        }

        // Ji<PERSON> belum login atau bukan admin, arahkan ke halaman login
        return redirect()->route('auth.login');
    }
}
