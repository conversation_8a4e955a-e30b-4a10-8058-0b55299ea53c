@extends('vip.app')

@section('title', 'Ticket Confirmation - KREEN VIP')

@push('styles')
<style>
.sticky-card {
    position: sticky;
    top: 2rem;
    z-index: 10;
}

.booking-container {
    position: relative;
    overflow: visible;
}

.booking-grid {
    position: relative;
    overflow: visible;
}

/* Full width section */
.full-width-section {
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    padding-left: calc(50vw - 50%);
    padding-right: calc(50vw - 50%);
}

/* Header with background image */
.header-bg {
    background-image: url('https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');
    background-size: cover;
    background-position: center;
    position: relative;
}

.header-bg::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(0,0,0,0.7), rgba(0,0,0,0.5));
    z-index: 1;
}

.header-content {
    position: relative;
    z-index: 2;
}

/* Mobile sticky adjustments */
@media (max-width: 1024px) {
    .sticky-card {
        position: relative;
        top: auto;
    }
}

/* Progress indicator responsive design */
@media (max-width: 768px) {
    .booking-progress {
        display: none !important;
    }

    .booking-progress-mobile {
        display: flex !important;
        justify-content: center;
        padding: 0.5rem 0;
        border-top: 1px solid #374151;
        margin-top: 1rem;
    }
}

@media (min-width: 769px) {
    .booking-progress-mobile {
        display: none !important;
    }
}

/* Success animation */
@keyframes checkmark {
    0% { transform: scale(0); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.success-checkmark {
    animation: checkmark 0.6s ease-in-out;
}

/* Ticket dotted line separator */
.ticket-separator {
    border-top: 2px dashed #ffffff;
    margin: 1.5rem 0;
    opacity: 0.6;
}

/* Better QR code styling */
.qr-code {
    background: white;
    padding: 0.75rem;
    border-radius: 0.375rem;
    display: inline-block;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* QR code pattern */
.qr-pattern {
    width: 8rem;
    height: 8rem;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
@endpush

@section('content')
<!-- Full Width Header with Background Image -->
<section class="full-width-section header-bg py-16">
    <div class="header-content text-center">
        <h1 class="text-4xl md:text-6xl font-bold text-white mb-4">{{ $order->venue_name }}</h1>
        <p class="text-gray-300 text-lg">{{ $order->venue_address }}</p>
    </div>
</section>

<!-- Combined Success Message and Ticket Details Section -->
<section class="full-width-section py-16 bg-gradient-to-b from-black to-gray-900">
    <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Success Message -->
        <div class="text-center mb-12">
            <!-- Success Icon -->
            <div class="success-checkmark mb-6">
                <div class="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto">
                    <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                </div>
            </div>

            <!-- Success Text -->
            <h2 class="text-white text-3xl font-bold mb-2">Payment Successful</h2>
            <p class="text-gray-300 text-lg mb-2">Now you can enjoy your night</p>
            <p class="text-gray-400 text-sm mb-12">don't forget to show your QR at the entrance</p>
        </div>

        <!-- Detailed Booking Information Card -->
        <div class="border-2 border-kreen-gold rounded-xl p-6 space-y-8" style="background: linear-gradient(98.07deg, #000000 0%, #2E2E2E 25.48%, #000000 50.48%, #464646 81.73%, #040404 100%);">
            <h3 class="text-white text-xl font-bold">Booking Details</h3>

            <!-- Venue Info -->
            <div class="flex items-center space-x-3">
                <div class="w-6 h-6 flex items-center justify-center flex-shrink-0">
                    <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <span class="text-white font-medium">{{ $order->venue_name }}</span>
            </div>

            <!-- Date -->
            <div class="flex items-center space-x-3">
                <div class="w-6 h-6 flex items-center justify-center flex-shrink-0">
                    <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <span class="text-white font-medium">{{ \Carbon\Carbon::parse($order->date)->format('l, d F Y') }}</span>
            </div>

            @foreach ($order_details as $order_detail)
            <div class="bg-white/20 rounded-lg p-6">
                <div class="flex items-center space-x-3 mb-4">
                    <div class="w-6 h-6 flex items-center justify-center flex-shrink-0">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2 6a2 2 0 012-2h12a2 2 0 012 2v2a2 2 0 100 4v2a2 2 0 01-2 2H4a2 2 0 01-2-2v-2a2 2 0 100-4V6z"/>
                        </svg>
                    </div>
                    <span class="text-white font-medium">{{ $order_detail->full_name }}</span>
                </div>

                <!-- QR Code -->
                <div class="text-center mb-4">
                    <div class="qr-code inline-block">
                        <div class="qr-pattern">
                            <!-- Simple QR code pattern -->
                            <img src="https://api.qrserver.com/v1/create-qr-code/?data={{ $order_detail->id_order_detail }}&size=150x150" alt="QR Code">
                        </div>
                    </div>
                    <p class="text-gray-300 text-sm mt-2">QR ID : {{ $order_detail->id_order_detail }}</p>
                </div>

                <!-- Ticket separator line -->
                <div class="ticket-separator"></div>

                <!-- Guest Details -->
                <div class="space-y-3 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-400">Full Name</span>
                        <span class="text-white">{{ $order_detail->full_name }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Email</span>
                        <span class="text-white">{{ $order_detail->email }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Phone Number</span>
                        <span class="text-white">{{ $order_detail->phone }}</span>
                    </div>
                </div>
            </div>
            @endforeach


            <!-- Payment Detail -->
            <div>
                <h4 class="text-white font-semibold mb-4">Payment Detail</h4>

                <div class="space-y-3 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-400">Booking Code</span>
                        <span class="text-white font-mono">{{ $order->invoice_number }}</span>
                    </div>

                    <div class="flex justify-between">
                        <span class="text-gray-400">Date</span>
                        <span class="text-white">{{ \Carbon\Carbon::parse($order->date)->format('l, d F Y') }}</span>
                    </div>
                    @foreach ($order_tickets as $order_ticket)
                    <div class="flex justify-between">
                        <span class="text-gray-400">{{ $order_ticket['nama_tiket'] }} (x{{ $order_ticket['qty'] }})</span>
                        <span class="text-white">{{ \App\Helpers\Format::formatCurrency($order_ticket['price'] * $order_ticket['qty'], 'IDR') }}</span>
                    </div>
                    @endforeach

                    <div class="flex justify-between">
                        <span class="text-gray-400">Service Fee</span>
                        <span class="text-white">{{ \App\Helpers\Format::formatCurrency($order->fees, 'IDR') }}</span>
                    </div>

                    <hr class="border-gray-600">

                    <div class="flex justify-between">
                        <span class="text-white font-medium">Total Payment</span>
                        <span class="text-kreen-gold font-bold text-lg">{{ \App\Helpers\Format::formatCurrency($order->total_amount, 'IDR') }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function () {
    // Add any interactive functionality here if needed
});

// Header Navigation Replacement for Confirmation Flow (Step 3)
function replaceHeaderNavigation() {
    // Hide existing navigation
    const desktopNav = document.querySelector('nav.hidden.md\\:flex');
    const loginButton = document.querySelector('a[href=""].hidden.md\\:inline-block');

    if (desktopNav) {
        desktopNav.style.display = 'none';
    }
    if (loginButton) {
        loginButton.style.display = 'none';
    }

    // Create progress indicator with step 3 active
    const headerContainer = document.querySelector('header .flex.items-center.justify-between');
    if (headerContainer && !document.querySelector('.booking-progress')) {
        const progressContainer = document.createElement('div');
        progressContainer.className = 'booking-progress flex items-center space-x-8';
        progressContainer.innerHTML = `
            <div class="flex items-center space-x-2">
                <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <span class="text-green-400 font-medium text-sm">Guest Info</span>
            </div>

            <div class="flex items-center">
                <div class="w-8 h-0.5 bg-green-500"></div>
                <svg class="w-3 h-3 text-green-500 mx-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                </svg>
            </div>

            <div class="flex items-center space-x-2">
                <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <span class="text-green-400 font-medium text-sm">Payment</span>
            </div>

            <div class="flex items-center">
                <div class="w-8 h-0.5 bg-green-500"></div>
                <svg class="w-3 h-3 text-green-500 mx-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                </svg>
            </div>

            <div class="flex items-center space-x-2">
                <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <span class="text-green-400 font-medium text-sm">You're In!</span>
            </div>
        `;

        // Insert progress indicator after logo
        const logoContainer = headerContainer.querySelector('.flex.items-center');
        if (logoContainer && logoContainer.nextElementSibling) {
            headerContainer.insertBefore(progressContainer, logoContainer.nextElementSibling);
        }
    }
}

// Initialize header replacement when page loads
replaceHeaderNavigation();
setTimeout(replaceHeaderNavigation, 100);

// Add mobile progress indicator
function addMobileProgress() {
    const mobileMenu = document.querySelector('#mobile-menu');
    if (mobileMenu && !document.querySelector('.booking-progress-mobile')) {
        const mobileProgress = document.createElement('div');
        mobileProgress.className = 'booking-progress-mobile md:hidden flex items-center justify-center space-x-4 px-4 py-3 bg-kreen-dark border-t border-gray-800';
        mobileProgress.innerHTML = `
            <div class="flex items-center space-x-1">
                <div class="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                    <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <span class="text-green-400 font-medium text-[10px] md:text-xs">Info</span>
            </div>

            <svg class="w-3 h-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>

            <div class="flex items-center space-x-1">
                <div class="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                    <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <span class="text-green-400 font-medium text-[10px] md:text-xs">Payment</span>
            </div>

            <svg class="w-3 h-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
            </svg>

            <div class="flex items-center space-x-1">
                <div class="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                    <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <span class="text-green-400 font-medium text-[10px] md:text-xs">In!</span>
            </div>
        `;

        mobileMenu.parentNode.insertBefore(mobileProgress, mobileMenu.nextSibling);
    }
}

addMobileProgress();
setTimeout(addMobileProgress, 100);
</script>
@endpush
