@extends('layouts.admin')

@section('title', 'Dashboard - Admin Panel')
@section('page-title', 'Home')

@section('content')
<div class="space-y-4 sm:space-y-6">
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        <!-- Tiket <PERSON> -->
        <div class="bg-white rounded-xl shadow-sm p-4 sm:p-6 border border-gray-100">
            <h3 class="text-xs sm:text-sm font-medium text-gray-600 mb-2 sm:mb-3">Tiket Masuk Terjual Hari Ini</h3>
            <div class="flex items-end justify-between">
                <div>
                    <p class="text-2xl sm:text-3xl font-bold text-gray-900 mb-1">{{ number_format($stats['tickets_sold_today'], 0, ',', '.') }}</p>
                    @if($stats['tickets_growth'] >= 0)
                        <p class="text-xs sm:text-sm text-green-600">+{{ $stats['tickets_growth'] }}% dibanding kemarin</p>
                    @else
                        <p class="text-xs sm:text-sm text-red-600">{{ $stats['tickets_growth'] }}% dibanding kemarin</p>
                    @endif
                </div>
            </div>
        </div>

        <!-- Pengunjung Masuk -->
        <div class="bg-white rounded-xl shadow-sm p-4 sm:p-6 border border-gray-100">
            <h3 class="text-xs sm:text-sm font-medium text-gray-600 mb-2 sm:mb-3">Pengunjung Masuk Hari Ini</h3>
            <div class="flex items-end justify-between">
                <div>
                    <p class="text-2xl sm:text-3xl font-bold text-gray-900 mb-1">{{ number_format($stats['visitors_today'], 0, ',', '.') }}</p>
                    <p class="text-xs sm:text-sm text-gray-500">Dari total {{ number_format($stats['total_bookings'], 0, ',', '.') }} pemesanan</p>
                </div>
            </div>
        </div>

        <!-- Pengunjung Selesai -->
        <div class="bg-white rounded-xl shadow-sm p-4 sm:p-6 border border-gray-100">
            <h3 class="text-xs sm:text-sm font-medium text-gray-600 mb-2 sm:mb-3">Pengunjung Selesai / Keluar</h3>
            <div class="flex items-end justify-between">
                <div>
                    <p class="text-2xl sm:text-3xl font-bold text-gray-900">{{ number_format($stats['visitors_completed'], 0, ',', '.') }}</p>
                </div>
            </div>
        </div>

        <!-- Estimasi Pendapatan -->
        <div class="bg-white rounded-xl shadow-sm p-4 sm:p-6 border border-gray-100">
            <h3 class="text-xs sm:text-sm font-medium text-gray-600 mb-2 sm:mb-3">Estimasi Pendapatan Hari Ini</h3>
            <div class="flex items-end justify-between">
                <div>
                    <p class="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">Rp {{ number_format($stats['estimated_revenue'], 0, ',', '.') }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Grid -->
    <div class="grid grid-cols-1 xl:grid-cols-2 gap-4 sm:gap-6">
        <!-- Event Terdekat -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100">
            <div class="p-4 sm:p-6 border-b border-gray-100">
                <h2 class="text-lg font-semibold text-gray-900">Event Terdekat</h2>
            </div>
            <div class="p-4 sm:p-6">
                <div id="events-container" class="space-y-4">
                    @forelse($upcomingEvents as $event)
                    <div class="flex items-start space-x-3 sm:space-x-4 event-item">
                        <img src="{{ $event->gambar }}" alt="{{ $event->nama }}" class="w-12 h-12 sm:w-16 sm:h-16 rounded-lg object-cover flex-shrink-0">
                        <div class="flex-1 min-w-0">
                            <h3 class="font-semibold text-gray-900 text-sm mb-1 line-clamp-2">{{ $event->nama }}</h3>
                            <p class="text-xs sm:text-sm text-gray-500 mb-1">{{ $event->formatted_date }}</p>
                            @if($event->lokasi)
                                <p class="text-xs text-gray-400">📍 {{ $event->lokasi }}</p>
                            @endif
                        </div>
                    </div>
                    @empty
                    <div class="text-center py-8">
                        <p class="text-gray-500 text-sm">Belum ada event terdekat</p>
                    </div>
                    @endforelse
                </div>
                
                @if($upcomingEvents->hasMorePages())
                <div class="mt-4 sm:mt-6 text-center">
                    <button id="load-more-events" class="w-full sm:w-auto px-4 sm:px-6 py-2 border-2 border-yellow-500 text-yellow-600 rounded-lg hover:bg-yellow-50 transition-colors font-medium text-sm" data-page="{{ $upcomingEvents->currentPage() + 1 }}">
                        <span class="button-text">Lihat Lainnya</span>
                        <span class="loading-text hidden">Loading...</span>
                    </button>
                </div>
                @endif
            </div>
        </div>

        <!-- Daftar Pengunjung -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100">
            <div class="p-4 sm:p-6 border-b border-gray-100">
                <h2 class="text-lg font-semibold text-gray-900">Daftar Pengunjung</h2>
            </div>
            <div class="p-4 sm:p-6">
                @if(count($visitors) > 0)
                    <div class="space-y-3 sm:space-y-4">
                        @foreach($visitors as $visitor)
                        <div class="flex items-center justify-between py-2">
                            <div class="flex items-center space-x-3 sm:space-x-4">
                                <span class="text-sm text-gray-500 w-4 sm:w-6 font-medium">{{ $visitor['id'] }}</span>
                                <span class="text-sm font-medium text-gray-900">{{ $visitor['name'] }}</span>
                            </div>
                            <span class="text-xs sm:text-sm text-gray-600 font-medium">{{ $visitor['status'] }}</span>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <p class="text-gray-500 text-sm">Belum ada pengunjung hari ini</p>
                    </div>
                @endif
                <!-- <div class="mt-4 sm:mt-6 text-center">
                    <button class="w-full sm:w-auto px-4 sm:px-6 py-2 border-2 border-yellow-500 text-yellow-600 rounded-lg hover:bg-yellow-50 transition-colors font-medium text-sm">
                        Lihat Lainnya
                    </button>
                </div> -->
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const loadMoreBtn = document.getElementById('load-more-events');
    const eventsContainer = document.getElementById('events-container');
    
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', function() {
            const page = this.getAttribute('data-page');
            const buttonText = this.querySelector('.button-text');
            const loadingText = this.querySelector('.loading-text');
            
            // Show loading state
            buttonText.classList.add('hidden');
            loadingText.classList.remove('hidden');
            this.disabled = true;
            
            fetch(`{{ route('admin.load-more-events') }}?page=${page}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.events.length > 0) {
                    // Add new events to container
                    data.events.forEach(event => {
                        const eventHtml = `
                            <div class="flex items-start space-x-3 sm:space-x-4 event-item">
                                <img src="${event.image_url}" alt="${event.nama}" class="w-12 h-12 sm:w-16 sm:h-16 rounded-lg object-cover flex-shrink-0">
                                <div class="flex-1 min-w-0">
                                    <h3 class="font-semibold text-gray-900 text-sm mb-1 line-clamp-2">${event.nama}</h3>
                                    <p class="text-xs sm:text-sm text-gray-500 mb-1">${event.formatted_date}</p>
                                    ${event.lokasi ? `<p class="text-xs text-gray-400">📍 ${event.lokasi}</p>` : ''}
                                </div>
                            </div>
                        `;
                        eventsContainer.insertAdjacentHTML('beforeend', eventHtml);
                    });
                    
                    // Update page number
                    this.setAttribute('data-page', data.currentPage + 1);
                    
                    // Hide button if no more pages
                    if (!data.hasMore) {
                        this.style.display = 'none';
                    }
                }
                
                // Reset button state
                buttonText.classList.remove('hidden');
                loadingText.classList.add('hidden');
                this.disabled = false;
            })
            .catch(error => {
                console.error('Error:', error);
                // Reset button state
                buttonText.classList.remove('hidden');
                loadingText.classList.add('hidden');
                this.disabled = false;
            });
        });
    }
});
</script>
@endsection