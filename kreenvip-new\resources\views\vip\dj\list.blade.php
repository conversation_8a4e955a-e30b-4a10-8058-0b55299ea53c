@extends('vip.app')
@section('title', 'Meet The DJs - KREEN VIP')

@push('styles')
<style>
/* DJ Grid Styles */
.dj-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

@media (min-width: 768px) {
    .dj-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .dj-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* DJ Card Styles */
.dj-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid rgba(212, 175, 55, 0.2);
}

.dj-card:hover {
    transform: translateY(-8px);
    border-color: rgba(212, 175, 55, 0.6);
    box-shadow: 0 20px 40px rgba(212, 175, 55, 0.1);
}

.dj-image {
    width: 100%;
    height: 300px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
}

.dj-image::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.3) 100%);
}

.dj-info {
    padding: 1.5rem;
    text-align: center;
}

.dj-name {
    color: white;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.dj-genre {
    color: #D4AF37;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.dj-origin {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
}

/* Filter Dropdowns */
.filter-container {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.filter-dropdown {
    position: relative;
    min-width: 150px;
}

.filter-select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.75rem 2.5rem 0.75rem 1rem;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 500;
    appearance: none;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.filter-select:focus {
    outline: none;
    border-color: #D4AF37;
    background: rgba(255, 255, 255, 0.15);
}

.filter-select option {
    background: #1a1a1a;
    color: white;
}

.filter-icon {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    width: 1.25rem;
    height: 1.25rem;
    color: #D4AF37;
    pointer-events: none;
}

/* Load More Button */
.load-more-btn {
    background: linear-gradient(135deg, #D4AF37, #B8941F);
    color: black;
    font-weight: 600;
    padding: 0.875rem 2rem;
    border-radius: 0.5rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.load-more-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(212, 175, 55, 0.3);
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .filter-container {
        gap: 1rem;
    }

    .filter-dropdown {
        min-width: 120px;
    }

    .dj-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .dj-image {
        height: 200px;
    }

    .dj-info {
        padding: 1rem;
    }
}
</style>
@endpush

@section('content')
<!-- Meet The DJs Page Background Container -->
<div class="explore-nights-bg-container2">
    <!-- Main Content -->
    <section class="explore-nights-content py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Page Header -->
            <div class="text-center mb-12">
                <h1 class="text-4xl md:text-5xl font-bold text-white mb-8">Meet The DJs</h1>

                <!-- Filter Dropdowns -->
                <div class="filter-container">
                    <!-- Origin Filter -->
                    <div class="filter-dropdown">
                        <select id="origin-filter" class="filter-select">
                            <option value="">Origin</option>
                            @foreach ($dj_origins as $origin)
                                <option value="{{ $origin->origin }}">{{ $origin->origin }}</option>
                            @endforeach
                        </select>
                        <svg class="filter-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
                        </svg>
                    </div>

                    <!-- Gender Filter -->
                    <div class="filter-dropdown">
                        <select id="gender-filter" class="filter-select">
                            <option value="">Gender</option>
                            <option value="male">Male</option>
                            <option value="female">Female</option>
                        </select>
                        <svg class="filter-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- DJs Grid -->
            <div class="dj-grid" id="dj-grid-from-ajax">

            </div>
            <!-- Load More Button -->
            <div class="text-center mt-12">
                <button class="load-more-btn" id="load-more-btn">
                    Load More DJs
                </button>
            </div>
        </div>
    </section>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function () {
    const djCards = document.querySelectorAll('.dj-card');
    const originFilter = document.getElementById('origin-filter');
    const genderFilter = document.getElementById('gender-filter');
    const loadMoreBtn = document.getElementById('load-more-btn');
    let currentPage = 1;

    // DJ card click handlers
    djCards.forEach(card => {
        card.addEventListener('click', function() {
            const djName = this.querySelector('.dj-name').textContent;
            console.log('DJ clicked:', djName);
            // Add navigation to DJ detail page here
            // window.location.href = `/dj/${djName.toLowerCase().replace(/\s+/g, '-')}`;
        });
    });

    // Filter functionality
    function filterDJs(isLoadMore = false) {
        const selectedOrigin = originFilter.value.toLowerCase();
        const selectedGender = genderFilter.value.toLowerCase();
        if (!isLoadMore) {
            currentPage = 1;
        }
        $.ajax({
            url: `{{ route('dj.ajaxListDj') }}`,
            type: 'GET',
            dataType: 'json',
            data: {
                origin: selectedOrigin,
                gender: selectedGender,
                page: currentPage,
                limit: 4
            },
            success: function(response) {
                const djGrid = document.getElementById('dj-grid-from-ajax');
                if (isLoadMore) {
                    djGrid.innerHTML += response.view;
                } else {
                    djGrid.innerHTML = response.view;
                }
                if(!response.has_more) {
                    loadMoreBtn.style.display = 'none'
                } else {
                    loadMoreBtn.style.display = 'inline-block';
                }
            }
        });
    }

    filterDJs();

    // Filter event listeners
    originFilter.addEventListener('change', () => filterDJs());
    genderFilter.addEventListener('change', () => filterDJs());

    // Load more functionality
    loadMoreBtn.addEventListener('click', function() {
        console.log('Load more DJs clicked');
        currentPage++;
        this.textContent = 'Loading...';
        this.disabled = true;
        filterDJs(true);

        // Simulate loading delay
        setTimeout(() => {
            this.textContent = 'Load More DJs';
            this.disabled = false;
        }, 1000);
    });

    // Smooth scroll effect for navbar
    const navbar = document.querySelector('.navbar, header, nav');
    if (navbar) {
        window.addEventListener('scroll', function() {
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
    }

    // Add entrance animation for DJ cards
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Initially hide cards for animation
    djCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        card.style.transitionDelay = `${index * 0.1}s`;
        observer.observe(card);
    });
});
</script>
@endpush
