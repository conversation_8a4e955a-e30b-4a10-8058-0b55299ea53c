<?php

namespace App\Helpers;

class Invoice
{

    public static function generateCodeInvoice()
    {
        $code = random_int(100000, 999999);
        return $code;
    }
    public static function generateInvoiceNumber($id_order, $code = null)
    {
        $prefix = 'INV/';
        $invoice_number = $prefix . $id_order . '/' . $code ?? self::generateCodeInvoice();
        return $invoice_number;
    }
}
