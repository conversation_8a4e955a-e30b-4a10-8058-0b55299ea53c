<?php

namespace App\Http\Controllers\Auth;

use App\Models\User;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Laravel\Socialite\Facades\Socialite;

class LoginController extends Controller
{
    public function logout()
    {
        Auth::logout();
        return redirect('/');
    }

    public function login()
    {
        return view('auth.login');
    }
    public function submitLogin(Request $request)
    {
        $data = $request->validate([
            'email' => 'required|string|email|max:255|exists:tbl_users,email',
            'password' => ['required', 'string', 'max:255', 'regex:/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d\W_]{8,}$/']

        ]);
        $data = $request->only(['email', 'password']);
        $auth = Auth::attempt($data);
        if ($auth) {
            $url_oauth = session()->get('url_oauth');
            $request->session()->regenerate();
            if ($url_oauth) {
                return redirect($url_oauth);
            }
            if (Auth::user()->role === "admin") {
                return redirect()->intended('/admin');
            } else {
                return redirect()->intended('/');
            }
        } else {
            return back()->withErrors(['email' => 'Invalid credentials']);
        }

    }


    public function googleRedirect()
    {
        return Socialite::driver('google')->stateless()->redirect();
    }

    // public function googleCallback()
    // {
    //     try {
    //         $user = Socialite::driver('google')->stateless()->user();
    //         $authUser = User::updateOrCreate([
    //             'email' => $user->email,
    //         ], [
    //             'name' => $user->name,
    //             'google_id' => $user->id,
    //             'google_token' => $user->token,
    //             'google_refresh_token' => $user->refreshToken,
    //         ]);
    //         Auth::login($authUser, true);
    //         request()->session()->regenerate();
    //         if (Auth::user()->role === "admin") {
    //             return redirect()->intended('/admin');
    //         } else {
    //             return redirect()->intended('/');
    //         }
    //     } catch (\Exception $e) {
    //         return redirect()->route('auth.login')->with('error', 'Google authentication failed. Please try again.');
    //     }
    // }


    public function googleCallback()
    {
        try {
            $googleUser = Socialite::driver('google')->stateless()->user();

            // Cek apakah user sudah ada berdasarkan email
            $existingUser = User::where('email', $googleUser->email)->first();

            if ($existingUser) {
                // Jika google_id belum diisi, isi sekarang
                if (!$existingUser->google_id) {
                    $existingUser->update([
                        'google_id' => $googleUser->id,
                        'google_token' => $googleUser->token,
                        'google_refresh_token' => $googleUser->refreshToken,
                    ]);
                } else {
                    // Update token saja
                    $existingUser->update([
                        'google_token' => $googleUser->token,
                        'google_refresh_token' => $googleUser->refreshToken,
                    ]);
                }

                $authUser = $existingUser;
            } else {
                // User belum ada, buat baru
                $authUser = User::create([
                    'name' => $googleUser->name,
                    'email' => $googleUser->email,
                    'google_id' => $googleUser->id,
                    'google_token' => $googleUser->token,
                    'google_refresh_token' => $googleUser->refreshToken,
                    'password' => bcrypt(str()->random(12)), // Optional, supaya bisa login manual nanti
                    'role' => 'user', // Optional, sesuaikan dengan default role kamu
                ]);
            }

            // Login dan regenerate session
            Auth::login($authUser, true);
            request()->session()->regenerate();

            // Redirect berdasarkan role
            return Auth::user()->role === 'admin'
                ? redirect()->intended('/admin')
                : redirect()->intended('/');

        } catch (\Exception $e) {
            // Logging jika dibutuhkan
            // \Log::error('Google Login Error: ' . $e->getMessage());

            return redirect()->route('auth.login')
                ->with('error', 'Google authentication failed. Please try again.');
        }
    }



}
