<?php

namespace App\Http\View\Composers;

use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;
use App\Models\BarVenue;
use App\Models\User;

class AdminComposer
{
    /**
     * Bind data to the view.
     */
    public function compose(View $view): void
    {
        $user = Auth::user();
        $merchantId = $user->id_merchant ?? null;
        
        // Default values
        $venueName = 'Admin Panel';
        $userName = $user->name ?? 'Admin User';
        
        // Get venue information if merchant ID exists
        if ($merchantId) {
            $venue = BarVenue::where('id_merchant', $merchantId)
                ->where('flag_aktif', '1')
                ->first();
                
            if ($venue) {
                $venueName = strtoupper($venue->nama);
            }
        }
        
        $view->with([
            'currentUser' => $user,
            'venueName' => $venueName,
            'userName' => $userName,
            'merchantId' => $merchantId
        ]);
    }
}
