/* KREEN VIP Custom Styles */

/* Custom Card Component */
.kreen-card {
    background: rgba(26, 26, 26, 0.95);
    border: 1px solid #333333;
    border-radius: 12px;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.kreen-card:hover {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

/* Golden Border Effect */
.kreen-golden-border {
    position: relative;
    border: 2px dashed transparent;
    border-radius: 12px;
    background:
        linear-gradient(#1a1a1a, #1a1a1a) padding-box,
        linear-gradient(
            90deg,
            #A37A1D 0%,
            #FFF0C0 28%,
            #FFEC95 66%,
            #281A00 100%
        ) border-box;
}

/* Smooth animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #000000;
}

    /* ::-webkit-scrollbar-thumb {
        background: #F59E0B;
        border-radius: 4px;
    } */

::-webkit-scrollbar-thumb:hover {
    background: #D97706;
}

/* Loading animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid #F59E0B;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .kreen-card {
        margin: 0.1rem;
        padding: 1.5rem;
    }
}

/* Focus states */
input:focus, button:focus {
    outline: none;
}

/* Smooth transitions for all interactive elements */
a, button, input, .kreen-card {
    transition: all 0.3s ease;
}


/* Custom Splide Styling */
.splide__arrow {
    background: #FFFFFF80 !important;
    border: none !important;
    width: 3rem !important;
    height: 3rem !important;
    border-radius: 50% !important;
    transition: all 0.3s ease !important;
}

.splide__arrow:hover {
    background: rgba(245, 158, 11, 1) !important;
    transform: scale(1.1) !important;
}

.splide__arrow svg {
    fill: black !important;
}

.splide__pagination__page {
    background: rgba(255, 255, 255, 0.3) !important;
    border: none !important;
    width: 12px !important;
    height: 12px !important;
    margin: 0 6px !important;
    transition: all 0.3s ease !important;
}

.splide__pagination__page.is-active {
    background: #ffffff !important;
    transform: scale(1.2) !important;
}

/* Hero Section Background */
.hero-section {
    background-image: url('https://images.unsplash.com/photo-1571266028243-d220c9c3b2d2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    position: relative;
}

.hero-section::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(0,0,0,0.8), rgba(0,0,0,0.6));
    z-index: 1;
}

.hero-section-content {
    position: relative;
    z-index: 2;
}

/* Golden Border Effect untuk Slider */
.kreen-golden-border {
    position: relative;
    border: 2px solid transparent;
    border-radius: 12px;
    background:
        linear-gradient(#1a1a1a, #1a1a1a) padding-box,
        linear-gradient(
            90deg,
            #A37A1D 0%,
            #FFF0C0 28%,
            #FFEC95 66%,
            #281A00 100%
        ) border-box;
}

/* Hero Slider Styling */
.hero-slide {
    position: relative;
    height: 400px;
    border-radius: 10px;
    overflow: hidden;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.hero-slide::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(45deg, rgba(0,0,0,0.7), rgba(0,0,0,0.4));
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 2rem;
    color: white;
}

/* Explore Section Background */
.explore-section {
    background-image: url('https://images.unsplash.com/photo-1571266028243-d220c9c3b2d2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    position: relative;
}

.explore-section::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(0,0,0,0.85), rgba(0,0,0,0.7));
    z-index: 1;
}

.explore-content {
    position: relative;
    z-index: 2;
}

/* Venue Card Styling */
.venue-card {
    background: rgba(26, 26, 26, 0.95);
    border: 1px solid #F59E0B;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
}

.venue-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(245, 158, 11, 0.3);
    border-color: #FCD34D;
}

.venue-image {
    height: 200px;
    background-size: cover;
    background-position: center;
    position: relative;
}

.venue-image::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(to bottom, transparent, rgba(0,0,0,0.3));
}


.kreen-golden-border {
    position: relative;
    border: 2px solid transparent;
    border-radius: 12px;
    background:
        linear-gradient(#1a1a1a, #1a1a1a) padding-box,
        linear-gradient(
            90deg,
            #A37A1D 0%,
            #FFF0C0 28%,
            #FFEC95 66%,
            #281A00 100%
        ) border-box;
}

/* Background Image Container untuk Hero + Explore Section */
.hero-explore-bg-container {
    background-image: url('../../image/kreenvip/home.jpg');
}

/* Background Image Container yang extend sampai navbar */
.hero-explore-bg-container {
    background-image:
    linear-gradient(
        to bottom,
        rgba(0, 0, 0, 0.95) 0%,
        rgba(0, 0, 0, 0.9) 15%,
        rgba(0, 0, 0, 0.85) 25%,
        rgba(0, 0, 0, 0.75) 35%,
        rgba(0, 0, 0, 0.7) 45%,
        rgba(0, 0, 0, 0.8) 60%,
        rgba(0, 0, 0, 0.95) 80%,
        rgba(0, 0, 0, 1) 90%,
        rgba(0, 0, 0, 1) 100%
    ),
        url('../../image/kreenvip/home.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    position: relative;
    /* Extend ke atas sampai navbar */
    margin-top: -80px; /* Sesuaikan dengan tinggi navbar */
    padding-top: 80px; /* Kompensasi margin */
    min-height: calc(100vh + 80px);
}

/* Pastikan navbar tetap di atas */
.navbar,
header,
nav {
    /* position: relative; */
    z-index: 100;
    /* background: transparent !important; */
}

/* Atau jika navbar perlu background gradient */
.navbar {
    background: linear-gradient(
        to bottom,
        rgba(0,0,0,0.9) 0%,
        rgba(0,0,0,0.7) 50%,
        rgba(0,0,0,0.3) 100%
    ) !important;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.hero-explore-content {
    position: relative;
    z-index: 2;
}

/* Adjust hero section untuk kompensasi extended background */
.hero-explore-content.mt-\[50px\] {
    margin-top: 50px; /* Tetap sama atau sesuaikan */
}

/* Mobile optimization */
@media (max-width: 768px) {
    .hero-explore-bg-container {
        background-attachment: scroll;
        margin-top: -70px; /* Sesuaikan untuk mobile navbar */
        padding-top: 70px;
        min-height: calc(100vh + 70px);
    }
}

/* Ensure smooth transition */
.hero-explore-bg-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 150px; /* Area transisi */
    background: linear-gradient(
        to bottom,
        rgba(0,0,0,0.3) 0%,
        transparent 100%
    );
    z-index: 1;
    pointer-events: none;
}

/* Text readability */
.hero-explore-content h2,
.hero-explore-content h3,
.hero-explore-content p {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}


/* Venue Item Simple Styling - TAMBAHAN YANG HILANG */
.venue-item-simple {
    transition: all 0.3s ease;
}

.venue-item-simple:hover {
    transform: translateY(-5px);
}

.venue-image-golden {
    height: 200px;
    background-size: cover;
    background-position: center;
    border-radius: 12px;
    position: relative;
    overflow: hidden;
    margin-bottom: 12px;
    transition: all 0.3s ease;
}

.venue-image-golden::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(to bottom, transparent, rgba(0,0,0,0.4));
}

.venue-item-simple:hover .venue-image-golden {
    transform: scale(1.02);
    box-shadow: 0 10px 30px rgba(245, 158, 11, 0.2);
}

.venue-info-simple {
    padding: 0;
}

.venue-info-simple h3 {
    font-size: 1.125rem;
    font-weight: 700;
    color: white;
    margin-bottom: 4px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.venue-info-simple h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: white;
    margin-bottom: 8px;
    opacity: 0.9;
}

.venue-info-simple .flex {
    align-items: center;
    font-size: 0.875rem;
    color: #9CA3AF;
}


/* NEW SECTIONS CSS - EVENTS & MEET THE DJs */

/* Events Section Styling */
.event-card {
    transition: all 0.3s ease;
    border-radius: 12px;
    overflow: hidden;
}

.event-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(245, 158, 11, 0.2);
}

.event-image {
    height: 280px;
    background-size: cover;
    background-position: center;
    border-radius: 12px;
    position: relative;
    overflow: hidden;
    margin-bottom: 12px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    /* background:
        linear-gradient(rgba(26, 26, 26, 0.1), rgba(26, 26, 26, 0.1)) padding-box,
        linear-gradient(
            90deg,
            #A37A1D 0%,
            #FFF0C0 28%,
            #FFEC95 66%,
            #281A00 100%
        ) border-box; */
}

.event-image::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(to bottom, transparent, rgba(0,0,0,0.6));
}

.event-date {
    position: absolute;
    top: 12px;
    left: 12px;
    background: rgba(245, 158, 11, 0.9);
    color: black;
    padding: 8px 12px;
    border-radius: 8px;
    text-align: center;
    font-weight: bold;
    z-index: 2;
}

.event-day {
    display: block;
    font-size: 1.25rem;
    font-weight: 700;
    line-height: 1;
}

.event-month {
    display: block;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.event-info {
    padding: 0;
}

.event-info h3 {
    font-size: 1.125rem;
    font-weight: 700;
    color: white;
    margin-bottom: 4px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.event-card:hover .event-image {
    transform: scale(1.02);
}

/* Meet The DJs Section Styling */
.dj-card {
    transition: all 0.3s ease;
    border-radius: 12px;
    overflow: hidden;
}

.dj-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(245, 158, 11, 0.2);
}

.dj-image {
    height: 280px;
    background-size: cover;
    background-position: center;
    border-radius: 12px;
    position: relative;
    overflow: hidden;
    margin-bottom: 12px;
    transition: all 0.3s ease;
}

.dj-image::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(to bottom, transparent, rgba(0,0,0,0.4));
}

.dj-info {
    padding: 0;
}

.dj-info h3 {
    font-size: 1.125rem;
    font-weight: 700;
    color: white;
    margin-bottom: 4px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.dj-card:hover .dj-image {
    transform: scale(1.02);
}

/* Specific class for Events grid - 2 columns on mobile */
.events-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
}

/* Specific class for DJs grid - normal responsive behavior */
.djs-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
}

/* Mobile optimization */
@media (max-width: 768px) {
    .event-image {
        height: 200px;
        margin-bottom: 8px;
    }

    .dj-image {
        height: 200px;
        margin-bottom: 8px;
    }

    .event-info h3,
    .dj-info h3 {
        font-size: 1rem;
    }

    .event-date {
        padding: 6px 10px;
    }

    .event-day {
        font-size: 1.1rem;
    }

    .event-month {
        font-size: 0.7rem;
    }

    /* Events: 2 columns on mobile */
    .events-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    /* DJs: 2 columns on tablet, 1 column on mobile */
    .djs-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .event-image {
        height: 180px;
    }

    .dj-image {
        height: 220px;
    }

    .event-info h3 {
        font-size: 0.9rem;
    }

    .dj-info h3 {
        font-size: 1rem;
    }

    .event-info p {
        font-size: 0.8rem;
    }

    /* Events: tetap 2 kolom */
    .events-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    /* DJs: 1 kolom di mobile kecil */
    .djs-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

@media (max-width: 360px) {
    .event-image {
        height: 160px;
    }

    .event-date {
        padding: 4px 8px;
        top: 8px;
        left: 8px;
    }

    .event-day {
        font-size: 1rem;
    }

    .event-month {
        font-size: 0.65rem;
    }

    .event-info h3 {
        font-size: 0.85rem;
    }

    /* Events: tetap 2 kolom bahkan di layar sangat kecil */
    .events-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
    }
}


/* Explore Events */

/* Background Image Container untuk Explore Nights Page */
.explore-nights-bg-container {
    background-image:
        linear-gradient(
            to bottom,
            rgba(0, 0, 0, 0.95) 0%,
            rgba(0, 0, 0, 0.9) 15%,
            rgba(0, 0, 0, 0.85) 25%,
            rgba(0, 0, 0, 0.75) 35%,
            rgba(0, 0, 0, 0.7) 45%,
            rgba(0, 0, 0, 0.8) 60%,
            rgba(0, 0, 0, 0.95) 80%,
            rgba(0, 0, 0, 1) 90%,
            rgba(0, 0, 0, 1) 100%
        ),
        url('../../image/kreenvip/home.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    position: relative;
    margin-top: 0;
    padding-top: 100px;
    min-height: 100vh;
}

.explore-nights-bg-container2 {
    background-image:
        url('../../image/kreenvip/nguyen1.png');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    position: relative;
    margin-top: 0;
    padding-top: 100px;
    min-height: 100vh;
}


.explore-nights-bg-vip {
    background-image:
        linear-gradient(
            to bottom,
            rgba(0, 0, 0, 0.95) 0%,
            rgba(0, 0, 0, 0.9) 15%,
            rgba(0, 0, 0, 0.85) 25%,
            rgba(0, 0, 0, 0.75) 35%,
            rgba(0, 0, 0, 0.7) 45%,
            rgba(0, 0, 0, 0.8) 60%,
            rgba(0, 0, 0, 0.95) 80%,
            rgba(0, 0, 0, 1) 90%,
            rgba(0, 0, 0, 1) 100%
        ),
        url('../../image/kreenvip/home.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    position: relative;
    margin-top: 0;
    padding-top: 100px;
    min-height: 100vh;
}



/* .explore-nights-bg-container {
    background-image:
        linear-gradient(
            to bottom,
            rgba(0, 0, 0, 0.95) 0%,
            rgba(0, 0, 0, 0.92) 10%,
            rgba(0, 0, 0, 0.88) 30%,
            rgba(0, 0, 0, 0.85) 50%,
            rgba(0, 0, 0, 0.90) 70%,
            rgba(0, 0, 0, 0.95) 100%
        ),
        url('../../image/kreenvip/banner_explore.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    position: relative;
    margin-top: 0;
    padding-top: 100px;
    min-height: 100vh;
} */


.explore-nights-content {
    position: relative;
    z-index: 2;

}

/* Explore Nights Grid Layout */
.explore-nights-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin-bottom: 2rem;
}

/* Venue Item Explore Styling */
.venue-item-explore {
    transition: all 0.3s ease;
}

.venue-item-explore:hover {
    transform: translateY(-5px);
}

.venue-image-explore {
    height: 200px;
    background-size: cover;
    background-position: center;
    border-radius: 12px;
    position: relative;
    overflow: hidden;
    margin-bottom: 12px;
    transition: all 0.3s ease;
}

.venue-image-explore::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(to bottom, transparent, rgba(0,0,0,0.4));
}

.venue-item-explore:hover .venue-image-explore {
    transform: scale(1.02);
    box-shadow: 0 10px 30px rgba(245, 158, 11, 0.2);
}

.venue-info-explore {
    padding: 0;
}

.venue-info-explore h3 {
    font-size: 1.125rem;
    font-weight: 700;
    color: white;
    margin-bottom: 4px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.venue-info-explore h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: white;
    margin-bottom: 8px;
    opacity: 0.9;
}

.venue-info-explore .flex {
    align-items: center;
    font-size: 0.875rem;
    color: #9CA3AF;
}

/* Text readability untuk explore nights page */
.explore-nights-content h1,
.explore-nights-content h2,
.explore-nights-content h3,
.explore-nights-content p {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

/* Mobile optimization untuk Explore Nights */
@media (max-width: 1024px) {
    .explore-nights-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .explore-nights-bg-container {
        background-attachment: scroll;
        padding-top: 80px;
        min-height: calc(100vh + 80px);
    }

    .explore-nights-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .venue-image-explore {
        height: 150px;
        margin-bottom: 8px;
    }

    .venue-info-explore h3 {
        font-size: 1rem;
    }

    .venue-info-explore h4 {
        font-size: 0.8rem;
    }

    .venue-info-explore .flex {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .explore-nights-bg-container {
        padding-top: 70px;
    }

    .explore-nights-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .venue-image-explore {
        height: 180px;
    }

    .venue-info-explore h3 {
        font-size: 1.1rem;
    }
}

/* Smooth transition untuk explore nights page */
.explore-nights-bg-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 150px;
    background: linear-gradient(
        to bottom,
        rgba(0,0,0,0.3) 0%,
        transparent 100%
    );
    z-index: 1;
    pointer-events: none;
}

/* Fix untuk mencegah horizontal scrolling di explore nights page */
.explore-nights-bg-container {
    width: 100%;
    max-width: 100vw;
    /* overflow-x: hidden; */
}

.bg-card-line {
    background: linear-gradient(98.07deg, #000000 0%, #2E2E2E 25.48%, #000000 50.48%, #464646 81.73%, #040404 100%);
}


.border-kreen-gold-one {
    border: 1px solid transparent;
    border-radius: 12px; /* bisa disesuaikan */
    padding: 2px; /* diperlukan agar border terlihat */
    background:
        linear-gradient(#1a1a1a, #1a1a1a) padding-box,
        linear-gradient(95.62deg, #A37A1D -0.27%, #FFF0C0 28.1%, #FFEC95 66.08%, #281A00 99.73%) border-box;
    background-origin: border-box;
    background-clip: padding-box, border-box;
    display: inline-block;
}


.kreen-golden-border-two {
    position: relative;
    border: 2px solid transparent;
    border-radius: 12px;
    padding: 2px;
    background:
        linear-gradient(#1a1a1a, #1a1a1a) padding-box,
        linear-gradient(
            90deg,
            #A37A1D 0%,
            #FFF0C0 28%,
            #FFEC95 66%,
            #281A00 100%
        ) border-box;
    background-origin: border-box;
    background-clip: padding-box, border-box;
    display: inline-block;
}

/* @media (max-width: 910px) {
    .hide-below-910 {
        display: none !important;
    }
} */

